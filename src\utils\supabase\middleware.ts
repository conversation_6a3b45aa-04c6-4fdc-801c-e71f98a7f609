import { createServerClient } from "@supabase/ssr";
import { NextResponse, type NextRequest } from "next/server";

export async function updateSession(request: NextRequest) {
	let supabaseResponse = NextResponse.next({
		request,
	});

	const supabase = createServerClient(
		process.env.NEXT_PUBLIC_SUPABASE_URL!,
		process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
		{
			cookies: {
				getAll() {
					return request.cookies.getAll();
				},
				setAll(cookiesToSet) {
					cookiesToSet.forEach(({ name, value }) =>
						request.cookies.set(name, value)
					);
					supabaseResponse = NextResponse.next({
						request,
					});
					cookiesToSet.forEach(({ name, value, options }) =>
						supabaseResponse.cookies.set(name, value, options)
					);
				},
			},
		}
	);

	// IMPORTANT: Avoid writing any logic between createClient and
	// supabase.auth.getUser(). A simple mistake could make it very hard to debug
	// issues with users being randomly logged out.

	const {
		data: { user },
	} = await supabase.auth.getUser();

	const publicRoutes = [
		"/login",
		"/signup",
		"/reset-password",
		"/update-password",
		"/auth",
		"/invite",
	];

	const isPublicRoute = publicRoutes.some((route) =>
		request.nextUrl.pathname.startsWith(route)
	);

	// If there is no user and it's not a public route, redirect to login
	if (!user && !isPublicRoute) {
		const url = request.nextUrl.clone();
		url.pathname = "/login";
		return NextResponse.redirect(url);
	}

	// If there is a user trying to access login, redirect to dashboard
	if (
		(user && request.nextUrl.pathname.startsWith("/login")) ||
		(user && request.nextUrl.pathname.startsWith("/signup"))
	) {
		const url = request.nextUrl.clone();
		url.pathname = "/";
		return NextResponse.redirect(url);
	}

	// If there is a user, check projects
	if (user && !isPublicRoute) {
		const { data, count } = await supabase
			.from("project_user")
			.select("*", { count: "exact" });
		const url = request.nextUrl.clone();
		// If on /onboarding and has projects, go to home
		if (
			request.nextUrl.pathname.startsWith("/onboarding") &&
			count &&
			count > 0
		) {
			url.pathname = "/";
			return NextResponse.redirect(url);
		}
		// If not on /onboarding and has no projects, go to onboarding
		if (
			!request.nextUrl.pathname.startsWith("/onboarding") &&
			(!count || count === 0)
		) {
			url.pathname = "/onboarding";
			return NextResponse.redirect(url);
		}

		// If user has only 1 project, redirect to project (/projectId) (if pathname is /)
		if (count && count === 1 && request.nextUrl.pathname === "/") {
			const project = data[0];
			const url = request.nextUrl.clone();
			url.pathname = `/${project.project_id}`;
			return NextResponse.redirect(url);
		}

		// Redirect /[numericProjectId] route to /[numericProjectId]/dashboard
		const pathname = request.nextUrl.pathname;
		const projectRootRegExp = /^\/\d+$/; // Only match if the path is /<numbers>
		if (pathname.match(projectRootRegExp)) {
			const redirectUrl = request.nextUrl.clone();
			redirectUrl.pathname = `${pathname}/dashboard`; // pathname is /<numericProjectId>
			return NextResponse.redirect(redirectUrl);
		}
	}

	// IMPORTANT: You *must* return the supabaseResponse object as it is. If you're
	// creating a new response object with NextResponse.next() make sure to:
	// 1. Pass the request in it, like so:
	//    const myNewResponse = NextResponse.next({ request })
	// 2. Copy over the cookies, like so:
	//    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())
	// 3. Change the myNewResponse object to fit your needs, but avoid changing
	//    the cookies!
	// 4. Finally:
	//    return myNewResponse
	// If this is not done, you may be causing the browser and server to go out
	// of sync and terminate the user's session prematurely!

	return supabaseResponse;
}
