"use client";

import * as React from "react";
import { format } from "date-fns";
import {
  Calendar as CalendarIcon,
  X,
  CalendarDays,
  Calendar,
} from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

export interface DateRangePickerProps {
  dateRange: {
    from: Date | undefined;
    to: Date | undefined;
  };
  onDateRangeChange: (from: Date | undefined, to: Date | undefined) => void;
  disabled?: boolean;
  isDisabledDate?: (date: Date) => boolean;
  placeholder?: string;
  align?: "center" | "start" | "end";
  className?: string;
}

export function DateRangePicker({
  dateRange,
  onDateRangeChange,
  disabled = false,
  isDisabledDate,
  placeholder = "Select date range",
  align = "start",
  className,
}: DateRangePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [internalRange, setInternalRange] = React.useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({ from: dateRange.from, to: dateRange.to });

  // Synchronize internal state with external props
  React.useEffect(() => {
    setInternalRange({ from: dateRange.from, to: dateRange.to });
  }, [dateRange.from, dateRange.to]);

  const handleDateSelection = (
    range: { from?: Date; to?: Date } | undefined
  ) => {
    if (!range) {
      // Clear selection
      setInternalRange({ from: undefined, to: undefined });
      onDateRangeChange(undefined, undefined);
      return;
    }

    const { from, to } = range;

    // Always update internal state for visual feedback
    setInternalRange({ from, to });

    // Only call onDateRangeChange (which may trigger a fetch) when both dates are selected
    if (from && to) {
      onDateRangeChange(from, to);
      setIsOpen(false);
    }

    // If same date is selected for both start and end, treat as a complete selection
    if (from && from === to) {
      onDateRangeChange(from, to);
      setIsOpen(false);
    }
  };

  // Format date to a readable format MM/dd/yyyy
  const formatDisplayDate = (date: Date | undefined) => {
    if (!date) return "";
    return format(date, "MM/dd/yyyy");
  };

  const displayText = React.useMemo(() => {
    if (internalRange.from && internalRange.to) {
      return `${formatDisplayDate(internalRange.from)} - ${formatDisplayDate(internalRange.to)}`;
    } else if (internalRange.from) {
      return `${formatDisplayDate(internalRange.from)} - Select end date`;
    }
    return placeholder;
  }, [internalRange.from, internalRange.to, placeholder]);

  return (
    <div className={className}>
      <Popover
        open={isOpen && !disabled}
        onOpenChange={(open) => !disabled && setIsOpen(open)}
      >
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal h-8 px-3",
              !internalRange.from && "text-muted-foreground",
              disabled && "opacity-70 cursor-not-allowed"
            )}
            disabled={disabled}
          >
            <CalendarIcon
              className={cn(
                "mr-2 h-4 w-4",
                internalRange.from && internalRange.to && "text-wd-blue"
              )}
            />
            <span className="truncate">{displayText}</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align={align}>
          <div className="space-y-4 p-3">
            <div className="flex justify-between gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setInternalRange({ from: undefined, to: undefined });
                  onDateRangeChange(undefined, undefined);
                  setIsOpen(false);
                }}
                className="flex-1"
              >
                <X className="mr-1 h-4 w-4 text-destructive" />
                Clear
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  const to = new Date();
                  const from = new Date();
                  from.setDate(to.getDate() - 7);
                  setInternalRange({ from, to });
                  onDateRangeChange(from, to);
                  setIsOpen(false);
                }}
                className="flex-1"
              >
                <CalendarDays className="mr-1 h-4 w-4 text-wd-primary-btncolor" />
                Last 7 days
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  const to = new Date();
                  const from = new Date();
                  from.setDate(to.getDate() - 30);
                  setInternalRange({ from, to });
                  onDateRangeChange(from, to);
                  setIsOpen(false);
                }}
                className="flex-1"
              >
                <Calendar className="mr-1 h-4 w-4 text-wd-blue" />
                Last 30 days
              </Button>
            </div>
            <CalendarComponent
              initialFocus
              mode="range"
              defaultMonth={internalRange.from}
              min={0}
              selected={{
                from: internalRange.from,
                to: internalRange.to,
              }}
              onSelect={(range) => {
                handleDateSelection(range);
              }}
              disabled={isDisabledDate}
              numberOfMonths={2}
            />
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
