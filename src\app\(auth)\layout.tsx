import Image from "next/image";
import writeDocsLogo from "@/assets/images/logoWriteDocs.svg";
import { Suspense } from "react";

export default async function DashboardLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	return (
		<div className='flex flex-col items-center justify-center h-screen min-h-fit py-4 bg-page-bg'>
			<Image
				className='pb-6 select-none'
				src={writeDocsLogo}
				alt='WriteDocs logo'
				sizes='100'
				priority
			/>
			<Suspense>{children}</Suspense>
		</div>
	);
}
