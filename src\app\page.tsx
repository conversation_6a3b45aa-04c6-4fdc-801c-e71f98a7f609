"use client";

import Image from "next/image";
import { useProject, useUser } from "@/contexts";
import { Plus, Briefcase } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import writedocsLogoSvg from "@/assets/images/logoWriteDocs.svg";
import { useRouter } from "next/navigation";
import { HomeSidebar } from "@/components/home-sidebar";
import { SidebarTrigger } from "@/components/ui/sidebar";

export default function ProjectSelectorPage() {
  const { projects, isLoading } = useProject();
  const { userProfile } = useUser();
  const router = useRouter();
  // TODO: Replace with actual user name
  const userName =
    userProfile?.fullName ||
    userProfile?.name ||
    userProfile?.email ||
    "<User>";

  const handleProjectClick = (projectId: string | number) => {
    router.push(`/${projectId}/dashboard`);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        Loading...
      </div>
    );
  }

  return (
    <div className="flex h-screen w-full">
      <HomeSidebar />
      <div className="flex-1 overflow-y-auto p-8 md:px-8">
        <SidebarTrigger />
        <header className="mb-8 pt-2">
          <div className="mb-8">
            <Image
              src={writedocsLogoSvg}
              alt="Writedocs Logo"
              className="h-8 w-auto"
            />
          </div>
          <h1 className="text-3xl font-semibold text-gray-800 dark:text-white">
            Hello, {userName}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Welcome to your projects!
          </p>
        </header>

        <hr className="mb-8 border-gray-200 dark:border-gray-700" />

        {!isLoading && projects.length > 0 && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {projects.map((project) => (
              <Card
                key={project.id.toString()}
                onClick={() => handleProjectClick(project.id)}
                className="hover:shadow-lg transition-shadow duration-200 ease-in-out transform hover:-translate-y-1 cursor-pointer"
              >
                <CardHeader className="items-center text-center p-6">
                  <Briefcase className="w-12 h-12 text-blue-600 dark:text-blue-400 mb-3" />
                  <CardTitle className="text-xl h-14 flex items-center justify-center overflow-hidden">
                    {project.project_name}
                  </CardTitle>
                </CardHeader>
                {project.website_url && (
                  <CardContent className="text-center p-0 pb-4">
                    <CardDescription className="truncate">
                      {project.website_url}
                    </CardDescription>
                  </CardContent>
                )}
              </Card>
            ))}

            {/* Create New Project Card */}
            <Card
              onClick={() => router.push("/new-project")}
              className="flex flex-col items-center justify-center hover:shadow-lg transition-shadow duration-200 ease-in-out transform hover:-translate-y-1 cursor-pointer border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 bg-gray-50 dark:bg-gray-700"
            >
              <CardHeader className="items-center text-center p-6">
                <Plus className="w-12 h-12 text-gray-400 dark:text-gray-500 mb-3" />
                <CardTitle className="text-xl text-gray-700 dark:text-white h-14 flex items-center justify-center overflow-hidden">
                  Create new project
                </CardTitle>
              </CardHeader>
            </Card>
          </div>
        )}

        {!isLoading && projects.length === 0 && (
          <div className="flex flex-col items-center justify-center p-10 bg-white dark:bg-gray-800 rounded-lg shadow-md mt-8">
            <Briefcase className="w-20 h-20 text-gray-300 dark:text-gray-600 mb-6" />
            <h2 className="text-2xl font-semibold text-gray-700 dark:text-white mb-2">
              No Projects Yet
            </h2>
            <p className="text-gray-500 dark:text-gray-400 mb-6 text-center">
              It looks like you haven&apos;t created any projects. <br />
              Get started by creating your first one!
            </p>
            {/* "Create New Project" button for empty state - can also be a Card */}
            <div
              onClick={() => router.push("/new-project")}
              className="flex flex-col items-center justify-center p-6 bg-blue-500 hover:bg-blue-600 text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 ease-in-out transform hover:-translate-y-1 cursor-pointer w-full max-w-xs"
            >
              <Plus className="w-8 h-8 mb-2" />
              <h2 className="text-lg font-semibold">Create new project</h2>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
