"use client";

import React, { useState } from "react";
import { Line, Pie } from "react-chartjs-2";
import {
	Chart as ChartJS,
	CategoryScale,
	LinearScale,
	PointElement,
	LineElement,
	Title,
	Tooltip as ChartTooltip,
	Legend as ChartLegend,
	ArcElement,
	Filler,
	ChartOptions,
	ChartData,
} from "chart.js";
// import UpgradePlan from "@/components/UpgradePlan"; // TODO: Re-evaluate if needed

// Registering necessary Chart.js components
ChartJS.register(
	CategoryScale,
	LinearScale,
	PointElement,
	LineElement,
	Title,
	ChartTooltip,
	ChartLegend,
	ArcElement,
	Filler
);

// Type for statistics data - Copied from old types.ts
export interface BotStatistics {
	totalCount: number;
	resolutionRate: string;
	couldAnswerRate: string;
	deflectionRate: string;
	timeSaved: number; // in minutes

	labels: string[]; // For line chart (e.g., dates)
	countData: number[]; // All questions
	negativeData: number[]; // Rated negative
	positiveData: number[]; // Rated positive
	couldAnswerData: number[]; // Could answer
	escalatedData: number[]; // Escalations

	// For Rating Distribution Pie Chart
	percentageLabels: string[]; // e.g., ["Unrated", "Rated Negative", "Rated Positive"]
	counts: number[]; // Corresponding counts for rating distribution

	// For Escalations Pie Chart
	escalatedLabels: string[]; // e.g., ["Escalated", "Deflected"]
	escalatedCounts: number[]; // Corresponding counts

	// For Questions Answered Pie Chart
	answerLabels: string[]; // e.g., ["Answered", "Unanswered"]
	answerCounts: number[]; // Corresponding counts
}

// Props interface for AskAIStatsTab
interface AskAIStatsTabProps {
	statistics: BotStatistics | null;
	isLoading: boolean;
	// planType?: string; // TODO: Re-evaluate if needed for UpgradePlan logic
}

// Specific type for our Pie chart data structure
interface CustomPieChartData {
	labels: string[];
	datasets: Array<{
		data: number[];
		backgroundColor: string[];
	}>;
}

const AskAIStatsTab: React.FC<AskAIStatsTabProps> = ({
	statistics,
	isLoading,
	// planType, // Prop commented out as it's not used yet
}) => {
	// States to control the visibility of items in the pie charts
	const [ratingLegendVisibility, setRatingLegendVisibility] = useState<
		boolean[]
	>([true, true, true]);
	const [escalationLegendVisibility, setEscalationLegendVisibility] = useState<
		boolean[]
	>([true, true]);
	const [answerLegendVisibility, setAnswerLegendVisibility] = useState<
		boolean[]
	>([true, true]);

	// Handlers to toggle the visibility of items in the legends
	const toggleRatingVisibility = (index: number) => {
		const newVisibility = [...ratingLegendVisibility];
		newVisibility[index] = !newVisibility[index];
		setRatingLegendVisibility(newVisibility);
	};

	const toggleEscalationVisibility = (index: number) => {
		const newVisibility = [...escalationLegendVisibility];
		newVisibility[index] = !newVisibility[index];
		setEscalationLegendVisibility(newVisibility);
	};

	const toggleAnswerVisibility = (index: number) => {
		const newVisibility = [...answerLegendVisibility];
		newVisibility[index] = !newVisibility[index];
		setAnswerLegendVisibility(newVisibility);
	};

	const formatTimeSaved = (minutes: number): string => {
		if (minutes < 60) {
			return `${minutes}m`;
		}
		const hours = Math.floor(minutes / 60);
		const remainingMinutes = minutes % 60;
		if (remainingMinutes === 0) {
			return `${hours}h`;
		}
		return `${hours}h ${remainingMinutes}m`;
	};

	// Line chart configuration
	const lineChartData: ChartData<"line"> = {
		labels: statistics?.labels || [],
		datasets: [
			{
				label: "All Questions",
				data: statistics?.countData || [],
				borderColor: "#BDD8FF", // Light Blue
				backgroundColor: "rgba(189, 216, 255, 0.2)",
				tension: 0.3,
				fill: true,
				pointRadius: 2,
				pointHoverRadius: 4,
			},
			{
				label: "Rated Negative",
				data: statistics?.negativeData || [],
				borderColor: "#FF4D51", // Red
				backgroundColor: "rgba(255, 77, 81, 0.2)",
				tension: 0.3,
				fill: true,
				pointRadius: 2,
				pointHoverRadius: 4,
			},
			{
				label: "Rated Positive",
				data: statistics?.positiveData || [],
				borderColor: "#3778FD", // Blue
				backgroundColor: "rgba(55, 120, 253, 0.2)",
				tension: 0.3,
				fill: true,
				pointRadius: 2,
				pointHoverRadius: 4,
			},
			{
				label: "Could Answer",
				data: statistics?.couldAnswerData || [],
				borderColor: "#152156", // Dark Blue
				backgroundColor: "rgba(21, 33, 86, 0.2)",
				tension: 0.3,
				fill: true,
				pointRadius: 2,
				pointHoverRadius: 4,
			},
			{
				label: "Escalations",
				data: statistics?.escalatedData || [],
				borderColor: "#FD834F", // Orange
				backgroundColor: "rgba(253, 131, 79, 0.2)",
				tension: 0.3,
				fill: true,
				pointRadius: 2,
				pointHoverRadius: 4,
			},
		],
	};

	// Rating pie chart configuration
	const ratingPieData: CustomPieChartData = {
		labels: statistics?.percentageLabels || [
			"Unrated",
			"Rated Negative",
			"Rated Positive",
		],
		datasets: [
			{
				data: statistics?.counts?.map((count: number, index: number) =>
					ratingLegendVisibility[index] ? count : 0
				) || [1, 0, 0], // Default to Unrated 100% if no data
				backgroundColor: ["#BDD8FF", "#FF4D51", "#3778FD"], // Light Blue, Red, Blue
			},
		],
	};

	// Escalation pie chart configuration
	const escalationPieData: CustomPieChartData = {
		labels: statistics?.escalatedLabels || ["Escalated", "Deflected"],
		datasets: [
			{
				data: statistics?.escalatedCounts?.map((count: number, index: number) =>
					escalationLegendVisibility[index] ? count : 0
				) || [0, 1], // Default to Deflected 100% if no data
				backgroundColor: ["#FF4D51", "#3778FD"], // Red, Blue
			},
		],
	};

	// Answer capability pie chart configuration
	const answerPieData: CustomPieChartData = {
		labels: statistics?.answerLabels || ["Answered", "Unanswered"],
		datasets: [
			{
				data: statistics?.answerCounts?.map((count: number, index: number) =>
					answerLegendVisibility[index] ? count : 0
				) || [1, 0], // Default to Answered 100% if no data
				backgroundColor: ["#3778FD", "#FF4D51"], // Blue, Red
			},
		],
	};

	const pieChartOptions: ChartOptions<"pie"> = {
		maintainAspectRatio: false,
		responsive: true,
		plugins: {
			legend: {
				display: false,
			},
			tooltip: {
				enabled: true,
			},
		},
	};

	const lineChartOptions: ChartOptions<"line"> = {
		maintainAspectRatio: false,
		responsive: true,
		plugins: {
			legend: {
				position: "top" as const,
				align: "start" as const,
				labels: {
					usePointStyle: true,
					pointStyle: "circle",
					padding: 20,
					boxWidth: 8,
					boxHeight: 8,
				},
			},
			tooltip: {
				enabled: true,
			},
		},
		scales: {
			y: {
				beginAtZero: true,
				grid: {
					display: true,
				},
				ticks: {
					precision: 0,
					stepSize: 0.2,
				},
			},
			x: {
				grid: {
					display: false,
				},
			},
		},
	};

	const statCardPulse = "animate-pulse bg-gray-200 dark:bg-gray-700 rounded";

	if (isLoading) {
		return (
			<div className='space-y-8'>
				{/* Skeleton for stats cards */}
				<div className='grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4'>
					{[...Array(5)].map((_, index) => (
						<div
							key={index}
							className='bg-white dark:bg-slate-800 p-4 rounded-lg shadow'
						>
							<div className={`h-5 w-3/5 mb-2 ${statCardPulse}`}></div>
							<div className={`h-9 w-2/5 ${statCardPulse}`}></div>
						</div>
					))}
				</div>

				{/* Skeleton for line chart */}
				<div className={`h-7 w-1/3 mb-3 ${statCardPulse}`}></div>
				<div className='bg-white dark:bg-slate-800 p-6 rounded-lg shadow'>
					<div className={`h-80 w-full ${statCardPulse}`}></div>
				</div>

				{/* Skeleton for pie charts */}
				<div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
					{[...Array(3)].map((_, index) => (
						<div
							key={index}
							className='bg-white dark:bg-slate-800 p-6 rounded-lg shadow flex flex-col items-center'
						>
							<div className={`h-6 w-1/2 mb-4 ${statCardPulse}`}></div>
							<div
								className={`h-52 w-52 mb-4 ${statCardPulse} rounded-full`}
							></div>
							<div className={`h-4 w-3/4 mb-2 ${statCardPulse}`}></div>
							<div className={`h-4 w-2/3 ${statCardPulse}`}></div>
						</div>
					))}
				</div>
			</div>
		);
	}

	if (!statistics) {
		// TODO: Handle case where there are no statistics (e.g. new bot, error after loading)
		// For now, showing a simple message. Could be a more elaborate empty state.
		return (
			<div className='text-center py-10'>
				<p className='text-gray-500 dark:text-gray-400'>
					No statistics data available yet.
				</p>
				{/* Consider adding a link to configuration or help docs */}
			</div>
		);
	}

	// TODO: Check planType to potentially render UpgradePlan
	// if (planType === 'free' && some_condition_for_stats_limit) {
	//   return <UpgradePlan message="Upgrade to view detailed statistics." />;
	// }

	const renderPieChartWithLegend = (
		title: string,
		chartData: CustomPieChartData,
		legendData: { label: string; color: string }[],
		visibilityState: boolean[],
		toggleVisibility: (index: number) => void
	) => {
		const activeChartData = chartData.datasets[0].data;
		const total =
			activeChartData.reduce((a: number, b: number) => a + b, 0) || 1; // Avoid division by zero
		const isEmpty =
			!activeChartData.some((d: number) => d > 0) ||
			activeChartData.length === 0;

		return (
			<div className='bg-white dark:bg-slate-800 p-6 rounded-lg shadow flex flex-col items-center h-full'>
				<h3 className='text-lg font-semibold text-gray-700 dark:text-gray-200 mb-4 text-center'>
					{title}
				</h3>
				<div className='w-full flex flex-col items-center mb-4 space-y-1'>
					{legendData.map((item, index) => {
						const percentage = (
							((activeChartData[index] || 0) / total) *
							100
						).toFixed(0);
						const legendItemClass = `flex items-center text-xs text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100 cursor-pointer`;
						const textClass = !visibilityState[index]
							? "line-through opacity-50"
							: "";

						return (
							<button
								key={item.label}
								onClick={() => toggleVisibility(index)}
								className={legendItemClass}
								disabled={!statistics}
							>
								<span
									className={`w-3 h-3 rounded-full mr-2 ${
										!visibilityState[index] ? "opacity-30" : ""
									}`}
									style={{ backgroundColor: item.color }}
								></span>
								<span className={textClass}>
									{percentage}% {item.label}
								</span>
							</button>
						);
					})}
				</div>
				<div className='relative w-52 h-52 sm:w-56 sm:h-56'>
					{isEmpty ? (
						<div className='absolute inset-0 flex items-center justify-center text-gray-400 dark:text-gray-500 text-sm'>
							No data
						</div>
					) : (
						<Pie
							data={chartData}
							options={pieChartOptions as ChartOptions<"pie">}
						/>
					)}
				</div>
			</div>
		);
	};

	return (
		<div className='space-y-8'>
			{/* Stats Cards */}
			<div className='grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4'>
				{[
					{
						title: "Total Questions",
						value: statistics.totalCount.toLocaleString(),
					},
					{ title: "Resolution Rate", value: `${statistics.resolutionRate}%` },
					{ title: "Answer Rate", value: `${statistics.couldAnswerRate}%` }, // Assuming couldAnswerRate is the "Answer Rate"
					{ title: "Deflection Rate", value: `${statistics.deflectionRate}%` },
					{ title: "Time Saved", value: formatTimeSaved(statistics.timeSaved) },
				].map((stat) => (
					<div
						key={stat.title}
						className='bg-white dark:bg-slate-800 p-4 rounded-lg shadow'
					>
						<p className='text-sm text-gray-500 dark:text-gray-400 mb-1'>
							{stat.title}
						</p>
						<p className='text-2xl font-semibold text-gray-800 dark:text-gray-100'>
							{stat.value}
						</p>
					</div>
				))}
			</div>

			{/* Question Trends Line Chart - Title outside the card */}
			<h3 className='text-xl font-semibold text-gray-700 dark:text-gray-200 mb-3'>
				Question Trends
			</h3>
			<div className='bg-white dark:bg-slate-800 p-6 pt-2 rounded-lg shadow mb-8'>
				<div className='h-80'>
					<Line
						data={lineChartData}
						options={lineChartOptions as ChartOptions<"line">}
					/>
				</div>
			</div>

			{/* Pie Charts Row */}
			<div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
				{renderPieChartWithLegend(
					"Rating Distribution",
					ratingPieData,
					[
						{ label: "Unrated", color: "#BDD8FF" },
						{ label: "Negative", color: "#FF4D51" },
						{ label: "Positive", color: "#3778FD" },
					],
					ratingLegendVisibility,
					toggleRatingVisibility
				)}
				{renderPieChartWithLegend(
					"Escalations",
					escalationPieData,
					[
						{ label: "Escalated", color: "#FF4D51" },
						{ label: "Deflected", color: "#3778FD" },
					],
					escalationLegendVisibility,
					toggleEscalationVisibility
				)}
				{renderPieChartWithLegend(
					"Questions Answered",
					answerPieData,
					[
						{ label: "Answered", color: "#3778FD" },
						{ label: "Unanswered", color: "#FF4D51" },
					],
					answerLegendVisibility,
					toggleAnswerVisibility
				)}
			</div>
		</div>
	);
};

export default AskAIStatsTab;
