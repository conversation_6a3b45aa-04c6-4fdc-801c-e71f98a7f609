"use client";

import { getUserProjects } from "@/lib/supabase/userProjects";
import { createClient } from "@/utils/supabase/client";
import { createContext, useContext, useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { Project, ProjectContextType, ProjectRecord } from "./types";
import { RealtimeChannel } from "@supabase/supabase-js";

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

export const ProjectProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProjectState] = useState<Project | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);
  const supabase = createClient();
  const params = useParams();
  const urlProjectId = params?.projectId as string | undefined;

  const determineProjectToSelect = (
    currentProjects: Project[],
    targetProjectId?: string
  ): Project | null => {
    if (currentProjects.length === 0) {
      return null;
    }
    if (targetProjectId) {
      const projectFromUrl = currentProjects.find(
        (p) => p.id.toString() === targetProjectId
      );
      if (projectFromUrl) {
        return projectFromUrl;
      }
    }
    const creatorProject = currentProjects.find((p) => p.is_creator);
    if (creatorProject) {
      return creatorProject;
    }
    return currentProjects[0];
  };

  useEffect(() => {
    const fetchProjectsAndHandleSelection = async () => {
      setIsLoading(true);
      try {
        const fetchedProjects = await getUserProjects();
        const currentProjects = fetchedProjects || [];
        setProjects(currentProjects);

        const projectToSelect = determineProjectToSelect(
          currentProjects,
          urlProjectId
        );
        setSelectedProjectState(projectToSelect);
      } catch (error) {
        console.error(
          "Error fetching projects or setting selected project:",
          error
        );
        setProjects([]);
        setSelectedProjectState(null);
      } finally {
        setIsLoading(false);
      }
    };
    fetchProjectsAndHandleSelection();
  }, [urlProjectId]);

  useEffect(() => {
    const refreshProjects = async () => {
      console.log(
        "Refreshing projects due to realtime event or URL project ID change..."
      );
      try {
        const fetchedProjects = await getUserProjects();
        const currentProjects = fetchedProjects || [];
        setProjects(currentProjects);

        setSelectedProjectState((prevSelectedProject) => {
          if (urlProjectId) {
            const projectFromUrl = currentProjects.find(
              (p) => p.id.toString() === urlProjectId
            );
            if (projectFromUrl) {
              if (
                JSON.stringify(projectFromUrl) !==
                JSON.stringify(prevSelectedProject)
              ) {
                return projectFromUrl;
              }
              return prevSelectedProject;
            }
          }

          if (prevSelectedProject) {
            const stillExistsAndAccessible = currentProjects.find(
              (p) => p.id === prevSelectedProject.id
            );
            if (stillExistsAndAccessible) {
              if (
                JSON.stringify(stillExistsAndAccessible) !==
                JSON.stringify(prevSelectedProject)
              ) {
                return stillExistsAndAccessible;
              }
              return prevSelectedProject;
            }
          }
          return determineProjectToSelect(currentProjects);
        });
      } catch (error) {
        console.error("Error refreshing projects:", error);
      }
    };

    if (!isLoading) {
      refreshProjects();
    }

    const projectsChannel: RealtimeChannel = supabase
      .channel("projects_changes")
      .on(
        "postgres_changes",
        { event: "INSERT", schema: "public", table: "projects" },
        (payload) => {
          console.log("Realtime: New project INSERT detected", payload);
          refreshProjects();
        }
      )
      .on(
        "postgres_changes",
        { event: "UPDATE", schema: "public", table: "projects" },
        (payload) => {
          console.log("Realtime: Project UPDATE detected", payload);
          const updatedRecord = payload.new as ProjectRecord;

          setProjects((currentProjects) =>
            currentProjects.map((p) =>
              p.id === updatedRecord.id ? { ...p, ...updatedRecord } : p
            )
          );
          setSelectedProjectState((currentSelected) => {
            if (currentSelected && currentSelected.id === updatedRecord.id) {
              const newSelectedState: Project = {
                ...currentSelected,
                ...updatedRecord,
              };
              if (
                JSON.stringify(newSelectedState) !==
                JSON.stringify(currentSelected)
              ) {
                return newSelectedState;
              }
            }
            if (urlProjectId && updatedRecord.id.toString() === urlProjectId) {
              return {
                ...(projects.find((p) => p.id === updatedRecord.id) ||
                  ({} as Project)),
                ...updatedRecord,
              };
            }
            return currentSelected;
          });
        }
      )
      .on(
        "postgres_changes",
        { event: "DELETE", schema: "public", table: "projects" },
        (payload) => {
          console.log("Realtime: Project DELETE detected", payload);
          const deletedRecord = payload.old as Partial<ProjectRecord>;
          const deletedProjectId = deletedRecord.id;
          if (deletedProjectId === undefined) return;

          let newSelectedProjectAfterDelete: Project | null = null;

          setProjects((prevProjects) => {
            const updatedProjects = prevProjects.filter(
              (p) => p.id !== deletedProjectId
            );
            newSelectedProjectAfterDelete = determineProjectToSelect(
              updatedProjects,
              urlProjectId
            );
            return updatedProjects;
          });
          setSelectedProjectState(newSelectedProjectAfterDelete);
        }
      )
      .subscribe((status, err) => {
        if (status === "SUBSCRIBED") {
          console.log("Realtime: Subscribed to 'projects' table changes!");
        } else if (err) {
          console.error(
            `Realtime: Error subscribing to 'projects' table: ${err.message}`
          );
        }
      });

    const projectUserChannel: RealtimeChannel = supabase
      .channel("project_user_changes")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: "project_user" },
        (payload) => {
          console.log(
            "Realtime: Project_user change detected",
            payload.eventType,
            payload
          );
          if (payload.eventType === "DELETE") {
            // const deletedLink = payload.old as Partial<ProjectUserRecord>; // Não precisamos mais do valor, apenas do evento
            // const affectedProjectId = deletedLink.project_id; // Variável não utilizada

            console.log(
              "Realtime: project_user DELETE might affect current selection or project list. Refreshing."
            );
            refreshProjects();
          } else {
            refreshProjects();
          }
        }
      )
      .subscribe((status, err) => {
        if (status === "SUBSCRIBED") {
          console.log("Realtime: Subscribed to 'project_user' table changes!");
        } else if (err) {
          console.error(
            `Realtime: Error subscribing to 'project_user' table: ${err.message}`
          );
        }
      });

    return () => {
      console.log("Realtime: Unsubscribing from channels.");
      supabase.removeChannel(projectsChannel);
      supabase.removeChannel(projectUserChannel);
    };
  }, [supabase, urlProjectId, isLoading]);

  const setSelectedProject = (project: Project | null) => {
    setSelectedProjectState(project);
  };

  return (
    <ProjectContext.Provider
      value={{
        projects,
        setProjects,
        selectedProject,
        setSelectedProject,
        isLoading,
      }}
    >
      {children}
    </ProjectContext.Provider>
  );
};

export const useProject = () => {
  const context = useContext(ProjectContext);
  if (!context) {
    throw new Error("useProject must be used within a ProjectProvider");
  }
  return context;
};
