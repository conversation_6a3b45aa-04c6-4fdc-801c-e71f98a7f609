"use server";

import DocsBot from "@/components/docsBotAi";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/app-sidebar";
import { cookies } from "next/headers";
import { getUserProjects } from "@/lib/supabase/userProjects";
import type { Project } from "@/contexts/ProjectContext/types";
import { notFound } from "next/navigation";

export default async function DashboardLayout({
	children,
	params: paramsPromise,
}: {
	children: React.ReactNode;
	params: Promise<{ projectId: string }>;
}) {
	const params = await paramsPromise;
	const projectId = params.projectId;

	const cookieStore = await cookies();
	const defaultOpen = cookieStore.get("sidebar_state")?.value === "true";

	if (!projectId) {
		notFound();
	}

	const projects: Project[] | null = await getUserProjects();

	const projectExists = projects?.some((p) => p.id.toString() === projectId);

	if (!projectExists) {
		notFound();
	}

	return (
		<SidebarProvider defaultOpen={defaultOpen}>
			<AppSidebar />
			<main className='w-full h-full'>
				<SidebarTrigger />
				<DocsBot />
				{children}
			</main>
		</SidebarProvider>
	);
}
