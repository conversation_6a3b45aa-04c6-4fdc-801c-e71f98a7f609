"use server";

import { getURL } from "@/utils/helpers";
import { createClient } from "@/utils/supabase/server";

/**
 * Sends a password reset email to the user.
 *
 * @param formData - Form data containing the user\'s email
 * @returns Object containing the status of the operation and relevant messages
 */
export async function sendPasswordResetEmail(formData: FormData) {
  const supabase = createClient();

  // Get email from form data
  const email = formData.get("email") as string;

  if (!email || typeof email !== "string" || email.trim() === "") {
    return {
      success: false,
      error: {
        message: "Please provide a valid email address.",
      },
    };
  }

  try {
    const { error } = await supabase.auth.resetPasswordForEmail(email.trim(), {
      redirectTo: `${getURL()}/update-password`,
    });

    if (error) {
      return {
        success: false,
        error: {
          message: error.message || "Error sending password reset email.",
        },
      };
    }

    return {
      success: true,
      message:
        "Password reset email sent successfully. Please check your inbox.",
    };
  } catch (error) {
    console.error("Error processing password reset:", error);
    return {
      success: false,
      error: {
        message:
          error instanceof Error
            ? error.message
            : "An error occurred while processing your request.",
      },
    };
  }
}
