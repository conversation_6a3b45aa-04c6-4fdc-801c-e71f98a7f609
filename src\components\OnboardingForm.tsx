"use client";

import { <PERSON><PERSON><PERSON><PERSON>, CardHeader } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Clipboard, Bell } from "lucide-react";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { useToast } from "@/components/ToastProvider";

const bellAnimation = `
@keyframes bellShake {
  0% { transform: rotate(0); }
  15% { transform: rotate(5deg); }
  30% { transform: rotate(-5deg); }
  45% { transform: rotate(4deg); }
  60% { transform: rotate(-4deg); }
  75% { transform: rotate(2deg); }
  85% { transform: rotate(-2deg); }
  92% { transform: rotate(1deg); }
  100% { transform: rotate(0); }
}
`;

interface OnboardingFormProps {
	onSuccess?: () => void;
	hasInvites?: boolean;
	onShowInvites?: () => void;
}

export function OnboardingForm({
	onSuccess,
	hasInvites,
	onShowInvites,
}: OnboardingFormProps) {
	const router = useRouter();
	const [subdomain, setSubdomain] = useState("");
	const [showPopover, setShowPopover] = useState(false);
	const [hasShown, setHasShown] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const { addToast } = useToast();

	const handleInputFocus = () => {
		if (!hasShown) {
			setShowPopover(true);
			setHasShown(true);
			setTimeout(() => {
				setShowPopover(false);
			}, 5000);
		}
	};

	const onboardingGithubSchema = z.object({
		projectName: z
			.string()
			.transform((s) => {
				s = s.replace(/\s+/g, "-");
				form.setValue("projectName", s);
				return s;
			})
			.transform((s) => {
				s = s.replace(/[^a-zA-Z0-9-.]/g, "");
				form.setValue("projectName", s);
				return s;
			})
			.transform((s) => {
				s = s.replace(/\.+/g, ".").replace(/-+/g, "-"); // remove pontos/hifens duplicados
				s = s.replace(/\.-/g, "-").replace(/-\./g, "."); // remove combinações de ponto-hifen
				form.setValue("projectName", s);
				return s;
			})
			.refine((s) => /^[a-zA-Z0-9]/.test(s), {
				message: "Must start with a letter or number",
			})
			.refine((s) => !s.includes(" "), { message: "Space not Allowed" })
			.refine((s) => !s.endsWith(".") && !s.endsWith("-"), {
				message: "Should end with letter or number",
			})
			.refine((s) => !/\.\./.test(s), {
				message: "Não é possível ter pontos consecutivos no nome do projeto",
			}),
	});

	const form = useForm<z.infer<typeof onboardingGithubSchema>>({
		resolver: zodResolver(onboardingGithubSchema),
		defaultValues: {
			projectName: "",
		},
	});

	async function onSubmit(values: z.infer<typeof onboardingGithubSchema>) {
		setIsLoading(true);
		values.projectName = values.projectName
			.replace(/(\.docs|-docs)$/, "")
			.toLowerCase();

		const formData = new FormData();

		Object.entries(values).forEach(([key, value]) => {
			formData.append(key, value);
		});

		try {
			const response = await fetch("/api/onboarding/project", {
				method: "POST",
				body: formData,
			});

			const data = await response.json();

			if (!response.ok) {
				throw new Error(data.error || "Something went wrong");
			}

			addToast("Project created successfully!", "success");
			onSuccess?.();
			router.push("/");
		} catch (error) {
			addToast(
				error instanceof Error ? error.message : "Failed to create project",
				"error"
			);
		} finally {
			setIsLoading(false);
		}
	}

	const copySubdomain = async () => {
		try {
			await navigator.clipboard.writeText(`${subdomain}.writedocs.io`);
			addToast("Subdomain copied to clipboard!", "success");
		} catch {
			addToast("Failed to copy subdomain", "error");
		}
	};

	useEffect(() => {
		const projectName = form.watch("projectName");
		setSubdomain(projectName.toLowerCase());
	}, [form.watch("projectName")]);

	return (
		<CardContent>
			<style jsx global>
				{bellAnimation}
			</style>
			<CardHeader>
				<div className='flex items-center justify-between'>
					<h2 className='text-lg font-medium text-primary-text'>
						Create a new project
					</h2>
					<Button
						variant='ghost'
						size='icon'
						className='relative'
						onClick={onShowInvites}
					>
						<Bell
							className={`h-5 w-5 text-[#4A5178] transition-all ${
								hasInvites ? "animate-[bellShake_1s_ease-in-out_infinite]" : ""
							}`}
						/>
						{hasInvites === true && (
							<span className='absolute top-0 right-0 h-2 w-2 rounded-full bg-red-600' />
						)}
					</Button>
				</div>
			</CardHeader>
			<Form {...form}>
				<form onSubmit={form.handleSubmit(onSubmit)} className='grid gap-3'>
					<FormField
						control={form.control}
						name='projectName'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Project name</FormLabel>
								<FormControl>
									<div className='relative'>
										<Input
											type='text'
											placeholder='Enter the project name'
											className='pr-14'
											onFocus={handleInputFocus}
											{...field}
										/>
										<Popover open={showPopover}>
											<PopoverTrigger asChild>
												<div className='absolute inset-y-0 right-3 flex items-center pointer-events-none text-muted-foreground'>
													-docs
												</div>
											</PopoverTrigger>
											<PopoverContent className='w-60' side='top'>
												<p className='text-sm'>
													The suffix &quot;-docs&quot; will be automatically
													added to the end of your project name.
												</p>
											</PopoverContent>
										</Popover>
									</div>
								</FormControl>
							</FormItem>
						)}
					/>

					<FormItem>
						<FormLabel>Subdomain</FormLabel>
						<div className='flex gap-2'>
							<div className='flex-1 relative'>
								<Input
									type='text'
									value={
										subdomain
											? `${subdomain}.docs.writedocs.io`
											: "projectname.docs.writedocs.io"
									}
									readOnly
									className='pr-10 text-muted-foreground'
								/>
								<Button
									type='button'
									variant='ghost'
									className='absolute right-0 top-0 h-full px-3 transition-colors hover:bg-transparent'
									onClick={copySubdomain}
									disabled={!subdomain}
								>
									<Clipboard className='h-4 w-4 text-muted-foreground transition-colors hover:text-foreground' />
								</Button>
							</div>
						</div>
					</FormItem>

					<Button
						type='submit'
						className='flex gap-1 mt-3'
						disabled={!form.formState.isValid || isLoading}
					>
						{isLoading ? "Creating..." : "Create"}
					</Button>
				</form>
			</Form>
		</CardContent>
	);
}
