import { createClient as createSupabaseClient } from "@supabase/supabase-js";

export async function createAdminClient() {
  // Criando um cliente Supabase diretamente com a service role
  return createSupabaseClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    }
  );
}
