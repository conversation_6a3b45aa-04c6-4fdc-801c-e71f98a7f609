"use client";

import { FolderGit2 } from "lucide-react";

import { SidebarMenuButton, SidebarMenuItem } from "@/components/ui/sidebar";
import { useProject } from "@/contexts/ProjectContext/ProjectContextProvider";
import React from "react";
import Link from "next/link";

export function ProjectsDropdownMenuItem() {
  const { projects, isLoading } = useProject();

  return (
    <SidebarMenuItem>
      <SidebarMenuButton
        className="cursor-pointer flex items-center w-full"
        size="lg"
        disabled={isLoading || projects.length === 0}
      >
        <Link
          href="/"
          className="flex flex-col items-center justify-center text-center flex-grow h-fit"
        >
          <FolderGit2 className="group-data-[state=expanded]:h-5 group-data-[state=expanded]:w-5 h-4 w-4" />
          <span className="group-data-[state=collapsed]:hidden">Projects</span>
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
}
