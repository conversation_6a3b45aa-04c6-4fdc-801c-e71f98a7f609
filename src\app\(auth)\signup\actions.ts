"use server";

import { createClient } from "@/utils/supabase/server";
import { createAdminClient } from "@/utils/supabase/admin";
import { getURL } from "@/utils/helpers";

export async function signup(formData: FormData) {
  const supabase = createClient();

  const data = {
    options: {
      data: { full_name: formData.get("name") as string },
      emailRedirectTo: `${getURL()}/auth/callback`,
    },
    email: formData.get("email") as string,
    password: formData.get("password") as string,
  };

  const { error } = await supabase.auth.signUp(data);

  if (error) {
    if (error.message === "User already registered") {
      throw new Error("An account with this email already exists");
    }
    throw new Error(error.message);
  }
}

export async function checkEmailExists(email: string) {
  try {
    const supabase = await createAdminClient();
    const { data, error } = await supabase.auth.admin.listUsers();
    const userExists = data?.users?.some((user) => user.email === email);
    if (error) throw error;

    return userExists || false;
  } catch (err) {
    console.error("Error checking email:", err);
    return false;
  }
}
