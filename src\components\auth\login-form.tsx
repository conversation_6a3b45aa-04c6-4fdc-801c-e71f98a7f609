"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { PasswordInput } from "@/components/auth/password-input";
import { useRouter } from "next/navigation";
import { emailLogin, samlSignIn } from "@/app/(auth)/login/actions";
import { useToast } from "@/components/ToastProvider";

import { OTPVerification } from "@/components/auth/otp-verification";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import { FieldErrors } from "react-hook-form";
import { createClient } from "@/utils/supabase/client";

// Unified schema: password is now optional.
// Validation for password presence will be handled by the standard form submission logic
// when not in SSO mode.
const schema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
});

export type LoginFormValues = z.infer<typeof schema>;

// Define props interface
interface LoginFormProps {
  ssoModeActive: boolean;
  setSsoModeActive: Dispatch<SetStateAction<boolean>>;
  isLoadingSso: boolean;
  setIsLoadingSso: Dispatch<SetStateAction<boolean>>;
  ssoError: string | null;
  setSsoError: Dispatch<SetStateAction<string | null>>;
}

export function LoginForm({
  ssoModeActive,
  setSsoModeActive,
  isLoadingSso,
  setIsLoadingSso,
  ssoError,
  setSsoError,
}: LoginFormProps) {
  const router = useRouter();
  const { addToast } = useToast();
  const [isEmailLoginLoading, setIsEmailLoginLoading] = useState(false);
  const [showOtpVerification, setShowOtpVerification] = useState(false);
  const [emailForOtp, setEmailForOtp] = useState("");
  const supabase = createClient();
  const form = useForm<LoginFormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      email: "",
      password: "",
    },
    mode: "onChange",
  });

  // Effect to clear form errors or reset form when ssoModeActive changes to false
  useEffect(() => {
    if (!ssoModeActive) {
      form.clearErrors();
      // form.reset(); // Uncomment this if you want to clear input fields as well
    }
  }, [ssoModeActive, form]);

  const onInvalid = (errors: FieldErrors<LoginFormValues>) => {
    if (errors.email?.message) {
      addToast(errors.email.message, "error");
    }
    if (errors.password?.message) {
      addToast(errors.password.message, "error");
    }
  };

  const onSubmit = async ({ email, password }: LoginFormValues) => {
    setIsEmailLoginLoading(true);
    if (ssoError) setSsoError(null);
    if (showOtpVerification) setShowOtpVerification(false);

    try {
      const formData = new FormData();
      formData.append("email", email);
      formData.append("password", password);

      // Chama a Server Action para tentar o login
      const result = await emailLogin(formData);

      if (result.success) {
        // Login bem-sucedido
        addToast("Login successful", "success");
        router.push("/onboarding");
        return;
      } else if (result.error) {
        // Login falhou, verificar o tipo de erro retornado pela Server Action
        if (result.error.type === "email_not_confirmed") {
          // Caso específico: e-mail ainda não confirmado pelo usuário
          setEmailForOtp(email);
          setShowOtpVerification(true);

          // Tenta reenviar o e-mail de verificação automaticamente
          const { error: resendError } = await supabase.auth.resend({
            type: "signup",
            email: email,
          });

          if (resendError) {
            addToast(
              "Failed to resend verification email. Please try again or contact support.",
              "error"
            );
          }
          // Informa ao usuário que o e-mail de verificação foi enviado (ou reenviado)
          addToast(result.error.message || "Please verify your email.", "info");
        } else {
          // Outros erros de login tratados (ex: credenciais incorretas)
          addToast(
            result.error.message ||
              "Login failed. Please check your credentials.",
            "error"
          );
        }
      } else {
        // Estrutura de resultado inesperada da Server Action (nem sucesso, nem erro definido)
        addToast(
          "An unexpected issue occurred during login. Try again or contact support.",
          "error"
        );
      }
    } catch (error) {
      // Captura erros não tratados pela Server Action (ex: falha de rede, erros de programação)
      // ou erros lançados intencionalmente (validação de campos obrigatórios no actions.ts)
      const errorMessage =
        error instanceof Error
          ? error.message
          : "An unknown error occurred. Please try again.";
      console.error("Login submission error:", error); // Loga o erro completo para depuração
      addToast(errorMessage, "error");
    } finally {
      setIsEmailLoginLoading(false);
    }
  };

  const handleSsoLogin = async () => {
    setSsoError(null);
    // Manually trigger email validation before proceeding
    const emailIsValid = await form.trigger("email");
    if (!emailIsValid) {
      // Display email validation error using toast
      if (form.formState.errors.email?.message) {
        addToast(form.formState.errors.email.message, "error");
      }
      return; // Email validation error will be shown by the form field
    }
    const email = form.getValues("email");

    setIsLoadingSso(true);
    try {
      const result = await samlSignIn(email);
      if (result.success && result.url) {
        window.location.href = result.url;
      } else if (result.error) {
        setSsoError(result.error.message);
      } else {
        setSsoError("An unexpected error occurred during SSO login.");
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        setSsoError(error.message);
      } else {
        setSsoError("Failed to initiate SSO login due to an unknown error.");
      }
    } finally {
      setIsLoadingSso(false);
    }
  };

  if (showOtpVerification) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
        <OTPVerification
          email={emailForOtp}
          onBack={() => {
            setShowOtpVerification(false);
            setEmailForOtp("");
          }}
          type="signup"
        />
      </div>
    );
  }

  return (
    <Form {...form}>
      {/* Bind onSubmitPasswordLogin if ssoModeActive is false */}
      <form
        onSubmit={
          !ssoModeActive
            ? form.handleSubmit(onSubmit, onInvalid)
            : (e) => e.preventDefault()
        }
        className="flex flex-col gap-4"
      >
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{ssoModeActive ? "Company Email" : "Email"}</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="<EMAIL>"
                  disabled={isLoadingSso}
                />
              </FormControl>
            </FormItem>
          )}
        />

        {!ssoModeActive && (
          <>
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <PasswordInput
                      placeholder="password"
                      {...field}
                      disabled={isLoadingSso}
                    />
                  </FormControl>
                  <Button
                    type="button"
                    form=""
                    onClick={() => {
                      if (isLoadingSso) return;
                      router.push("/reset-password");
                    }}
                    className="ml-auto text-right text-xs p-0 pt-2 h-fit text-wd-placeholder-color cursor-pointer hover:underline disabled:opacity-50"
                    variant="link"
                    disabled={isLoadingSso}
                  >
                    Forgot your password?
                  </Button>
                </FormItem>
              )}
            />
            {/* This button submits the form for email/password login */}
            <Button
              type="submit"
              variant="blue"
              disabled={
                isLoadingSso ||
                isEmailLoginLoading ||
                (!form.formState.isValid && form.formState.isSubmitted)
              }
            >
              {isEmailLoginLoading ? "Processing..." : "Continue"}
            </Button>
          </>
        )}

        {ssoError && (
          <div
            className="text-xs text-red-500 p-2 bg-red-50 border border-red-200 rounded-md"
            dangerouslySetInnerHTML={{ __html: ssoError }}
          />
        )}

        {ssoModeActive ? (
          <>
            <Button
              type="button" // Important: type='button' to prevent form submission
              variant="blue"
              onClick={handleSsoLogin} // Specific handler for SSO
              disabled={isLoadingSso}
              className="w-full"
            >
              {isLoadingSso ? "Processing..." : "Continue with SSO"}
            </Button>
          </>
        ) : (
          <Button
            type="button"
            variant="secondary"
            onClick={() => {
              setSsoModeActive(true);
              setSsoError(null);
              form.clearErrors("password"); // Clear password error specifically when switching
            }}
            disabled={isLoadingSso}
          >
            Sign in with Enterprise SSO
          </Button>
        )}
      </form>
    </Form>
  );
}
