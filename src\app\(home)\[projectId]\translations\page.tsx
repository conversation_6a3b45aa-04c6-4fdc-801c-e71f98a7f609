"use client";

import { useParams } from "next/navigation";
import { useProject } from "@/contexts";

export default function TranslationsPage() {
	const { projectId } = useParams();
	const { projects, isLoadingProjects } = useProject();

	if (isLoadingProjects) {
		return <p>Loading...</p>;
	}

	const project = projects.find((p) => p.id.toString() === projectId);

	return (
		<div className='p-8'>
			<h1 className='text-3xl font-semibold'>
				{project?.project_name} - Translations
			</h1>
		</div>
	);
}
