import { NextRequest, NextResponse } from "next/server";
import { validateDocsBotKey, fetchDocsBotQuestions } from "../utils/docsbot";
import { BotQuestion } from "@/app/(home)/ask-ai/types";

// Main GET function to handle export-csv requests
export async function GET(request: NextRequest) {
	console.log(
		"[ASK-AI API] GET /api/ask-ai/export-csv - Exporting questions to CSV"
	);
	try {
		// Get API key from environment variables
		const apiKey = process.env.DOCSBOT_API_KEY;
		if (!apiKey) {
			return NextResponse.json(
				{ error: "API key not configured" },
				{ status: 500 }
			);
		}

		// Get query parameters from the request
		const { searchParams } = new URL(request.url);
		const docsbotKey = searchParams.get("docsbotKey");
		const rating = searchParams.get("rating");
		const escalated = searchParams.get("escalated");
		const couldAnswer = searchParams.get("couldAnswer");
		const startDate = searchParams.get("startDate");
		const endDate = searchParams.get("endDate");

		try {
			// Validate docsbotKey and extract teamId and botId
			const { teamId, botId } = validateDocsBotKey(docsbotKey);

			// Fetch all questions with pagination
			const allQuestions = await fetchAllQuestionsWithPagination(
				teamId,
				botId,
				apiKey,
				rating,
				escalated,
				couldAnswer,
				startDate,
				endDate
			);

			// Generate CSV content
			const csvContent = generateCSV(allQuestions);

			// Set the response headers for CSV download
			const headers = {
				"Content-Type": "text/csv;charset=utf-8",
				"Content-Disposition": `attachment; filename="ask-ai-logs.csv"`,
			};

			return new NextResponse(csvContent, { status: 200, headers });
		} catch (error) {
			if (error instanceof Error) {
				return NextResponse.json({ error: error.message }, { status: 400 });
			}
			throw error;
		}
	} catch (error) {
		console.error("Error in export-csv API route:", error);

		return NextResponse.json(
			{
				success: false,
				error: "Internal server error",
				details: error instanceof Error ? error.message : String(error),
			},
			{ status: 500 }
		);
	}
}

// Function to fetch all questions with pagination
async function fetchAllQuestionsWithPagination(
	teamId: string,
	botId: string,
	apiKey: string,
	rating: string | null,
	escalated: string | null,
	couldAnswer: string | null,
	startDate: string | null,
	endDate: string | null
): Promise<BotQuestion[]> {
	const allQuestions: BotQuestion[] = [];
	let page = 0;
	let hasMore = true;
	const MAX_PAGES = 100; // Safety limit to prevent infinite loops

	while (hasMore && page < MAX_PAGES) {
		const data = await fetchDocsBotQuestions(
			teamId,
			botId,
			apiKey,
			page.toString(),
			rating,
			escalated,
			couldAnswer,
			startDate,
			endDate
		);

		allQuestions.push(...data.questions);

		// Check if there are more pages
		hasMore = data.questions.length > 0 && data.pagination.hasMorePages;
		page++;
	}

	return allQuestions;
}

// Function to generate CSV content from questions
function generateCSV(questions: BotQuestion[]): string {
	// Define CSV headers
	const headers = [
		"Date",
		"Question",
		"Answer",
		"Rating",
		"Escalated",
		"Could Answer",
		"Sources",
	];

	// Create CSV content
	let csvContent = headers.join(",") + "\n";

	// Add data rows
	questions.forEach((question) => {
		const row = [
			// Format date as MM/DD/YYYY HH:MM:SS
			formatDate(new Date(question.createdAt)),
			// Escape quotes and commas in text fields
			escapeCsvField(question.question),
			escapeCsvField(question.answer),
			// Format rating as text
			formatRating(question.rating),
			// Format boolean values
			question.escalation ? "Yes" : "No",
			question.couldAnswer ? "Yes" : "No",
			// Format sources as URLs separated by space
			escapeCsvField((question.sources || []).map((s) => s.url).join(" ")),
		];

		csvContent += row.join(",") + "\n";
	});

	return csvContent;
}

// Helper function to format date
function formatDate(date: Date) {
	const month = (date.getMonth() + 1).toString().padStart(2, "0");
	const day = date.getDate().toString().padStart(2, "0");
	const year = date.getFullYear();
	const hours = date.getHours().toString().padStart(2, "0");
	const minutes = date.getMinutes().toString().padStart(2, "0");
	const seconds = date.getSeconds().toString().padStart(2, "0");

	return `${month}/${day}/${year} ${hours}:${minutes}:${seconds}`;
}

// Helper function to escape CSV fields
function escapeCsvField(field: string) {
	if (field === null || field === undefined) {
		return "";
	}

	// Escape quotes by doubling them and wrap in quotes if needed
	const stringField = String(field);
	if (
		stringField.includes(",") ||
		stringField.includes('"') ||
		stringField.includes("\n")
	) {
		return `"${stringField.replace(/"/g, '""')}"`;
	}
	return stringField;
}

// Helper function to format rating
function formatRating(rating: number | null) {
	if (rating === null || rating === undefined) {
		return "No Rating";
	}

	switch (rating) {
		case 1:
			return "Helpful";
		case -1:
			return "Unhelpful";
		case 0:
		default:
			return "No Rating";
	}
}
