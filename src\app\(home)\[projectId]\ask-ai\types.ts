// Type for statistics data
export interface BotStatistics {
	totalCount: number;
	resolutionRate: string;
	couldAnswerRate: string;
	deflectionRate: string;
	timeSaved: number;
	isMockData?: boolean; // Flag to identify mock data
	isPlaceholder?: boolean; // Flag to identify placeholder data

	labels: string[];
	countData: number[];
	negativeData: number[];
	positiveData: number[];
	couldAnswerData: number[];
	escalatedData: number[];

	percentageLabels: string[];
	counts: number[];

	escalatedLabels: string[];
	escalatedCounts: number[];

	answerLabels: string[];
	answerCounts: number[];
}

// Define the tab types
export type TabType = "stats" | "logs" | "design";

// Types for logs data
export interface DocumentSource {
	title: string;
	url: string;
	sourceId: string;
	content: string;
	page: string | null;
	type: string;
	used: boolean;
}

export interface QuestionMetadata {
	referrer: string;
	[key: string]: unknown;
}

export interface BotQuestion {
	id: string;
	createdAt: string;
	couldAnswer: boolean;
	alias: string;
	question: string;
	answer: string;
	rating: number;
	sources: DocumentSource[];
	escalation: boolean;
	ip: string;
	metadata: QuestionMetadata;
	standaloneQuestion: string;
	run_id: string;
	testing?: boolean;
}

// Pagination data from DocsBOT API
export interface DocsBotPagination {
	perPage: number;
	page: number;
	viewableCount: number;
	totalCount: number;
	planLimit: string;
	hasMorePages: boolean;
}

// API response format from DocsBOT
export interface DocsBotQuestionsResponse {
	questions: BotQuestion[];
	pagination: DocsBotPagination;
}
