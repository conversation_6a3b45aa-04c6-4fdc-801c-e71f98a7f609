import { createClient } from "@/utils/supabase/server";
import { NextResponse } from "next/server";

export async function GET() {
	console.log("[MY-INVITES API] GET /api/my-invites - Fetching user invites");
	try {
		const supabase = createClient();
		const {
			data: { user },
		} = await supabase.auth.getUser();

		if (!user) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		const { data: invites, error } = await supabase
			.from("project_invites")
			.select(
				`
        *,
        project:projects(id, project_name)
      `
			)
			.eq("email", user.email)
			.eq("status", "invited");

		if (error) {
			console.error("Query error:", error);
			throw error;
		}

		return NextResponse.json({ invites });
	} catch (error) {
		console.error("Error fetching invites:", error);
		return NextResponse.json(
			{ error: "Error on get invites" },
			{ status: 500 }
		);
	}
}

export async function PATCH(request: Request) {
	console.log(
		"[MY-INVITES API] PATCH /api/my-invites - Updating invite status"
	);
	try {
		const supabase = createClient();
		const {
			data: { user },
		} = await supabase.auth.getUser();

		if (!user) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		const { inviteId, projectId } = await request.json();

		// Iniciar uma transação usando RPC
		const { data, error: rpcError } = await supabase.rpc(
			"accept_project_invite",
			{
				p_invite_id: inviteId,
				p_project_id: projectId,
				p_user_id: user.id,
				p_user_email: user.email,
			}
		);

		if (rpcError) {
			console.error("RPC Error:", rpcError);
			throw rpcError;
		}

		return NextResponse.json({ success: true, data });
	} catch (error) {
		console.error("Error accepting invite:", error);
		return NextResponse.json(
			{
				error: "Erro ao aceitar convite",
				details: error instanceof Error ? error.message : "no hints",
			},
			{ status: 500 }
		);
	}
}

export async function DELETE(request: Request) {
	console.log("[MY-INVITES API] DELETE /api/my-invites - Deleting invite");
	try {
		const { inviteId } = await request.json();
		const supabase = createClient();

		const { error } = await supabase
			.from("project_invites")
			.delete()
			.eq("id", inviteId);

		if (error) throw error;

		// Retornar uma resposta vazia mas com status 200
		return new NextResponse(null, { status: 200 });
	} catch (error) {
		console.error("Error rejecting invite:", error);
		return NextResponse.json(
			{ error: "Error rejecting invite" },
			{ status: 500 }
		);
	}
}
