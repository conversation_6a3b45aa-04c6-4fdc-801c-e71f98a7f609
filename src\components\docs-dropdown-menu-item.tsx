import { useState, useEffect } from "react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { BookOpenIcon } from "lucide-react";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SidebarMenuButton, SidebarMenuItem } from "@/components/ui/sidebar";
import { useProject } from "@/contexts/ProjectContext/ProjectContextProvider";
import { Project } from "@/contexts/ProjectContext/types";

export function DocsDropdownMenuItem() {
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);
	const { projects } = useProject();
	const params = useParams();
	const projectIdFromUrl = params.projectId as string;

	const [currentProject, setCurrentProject] = useState<
		Project | null | undefined
	>(undefined);

	useEffect(() => {
		if (projectIdFromUrl && projects.length > 0) {
			const foundProject = projects.find(
				(p) => p.id.toString() === projectIdFromUrl
			);
			setCurrentProject(foundProject || null);
		} else {
			setCurrentProject(null);
		}
	}, [projectIdFromUrl, projects]);

	return (
		<SidebarMenuItem>
			<DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
				<DropdownMenuTrigger asChild>
					<SidebarMenuButton
						size='lg'
						className='w-full group cursor-pointer focus-visible:ring-0 focus:ring-transparent'
					>
						<div className='flex flex-col items-center justify-center text-center flex-grow h-fit py-1'>
							<BookOpenIcon
								size={20}
								className='transition-colors group-data-[state=expanded]:h-5 group-data-[state=expanded]:w-5 h-4 w-4'
								strokeWidth={2}
							/>
							<span className='text-xs transition-colors group-data-[state=collapsed]:hidden'>
								Docs
							</span>
						</div>
					</SidebarMenuButton>
				</DropdownMenuTrigger>
				<DropdownMenuContent
					side='right'
					align='start'
					sideOffset={10}
					className='w-56'
				>
					<DropdownMenuLabel className='px-3 py-2 border-b border-gray-100'>
						<span className='text-xs font-medium text-gray-400'>
							Live Documentation
						</span>
					</DropdownMenuLabel>

					{currentProject?.website_url && (
						<DropdownMenuItem
							asChild
							className='px-3 py-1.5 text-sm cursor-pointer'
						>
							<Link
								href={`https://${currentProject.website_url}`}
								target='_blank'
								className='w-full block'
							>
								Your Documentation
							</Link>
						</DropdownMenuItem>
					)}

					{currentProject?.website_url && (
						<DropdownMenuSeparator className='my-1' />
					)}

					<DropdownMenuItem
						asChild
						className='px-3 py-1.5 text-sm cursor-pointer'
					>
						<Link href='https://docs.writedocs.io/' className='w-full block'>
							WriteDocs Documentation
						</Link>
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>
		</SidebarMenuItem>
	);
}
