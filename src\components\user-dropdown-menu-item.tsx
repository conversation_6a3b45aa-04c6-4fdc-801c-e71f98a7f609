"use client";

import { UserCircle, LogOut } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SidebarMenuButton, SidebarMenuItem } from "@/components/ui/sidebar";
import { useUser } from "@/contexts";
import { signOut } from "@/app/auth/signOut";

interface UserDropdownMenuItemProps {
	className?: string;
}

export function UserDropdownMenuItem({ className }: UserDropdownMenuItemProps) {
	const { userProfile, isUserLoading } = useUser();

	const displayName =
		userProfile?.fullName || userProfile?.name || userProfile?.email;

	if (isUserLoading) {
		return (
			<SidebarMenuItem className={className}>
				<SidebarMenuButton
					className='flex items-center justify-center w-full'
					size='lg'
					disabled
				>
					<div className='flex flex-col items-center justify-center text-center flex-grow h-fit'>
						<Avatar className='h-10 w-10'>
							<AvatarFallback>
								<UserCircle className='h-5 w-5' />
							</AvatarFallback>
						</Avatar>
						<span className='hidden group-data-[state=expanded]:inline text-xs text-muted-foreground mt-1'>
							Loading...
						</span>
					</div>
				</SidebarMenuButton>
			</SidebarMenuItem>
		);
	}

	if (!userProfile) {
		return null;
	}

	return (
		<SidebarMenuItem className={className}>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<SidebarMenuButton
						className='cursor-pointer flex items-center justify-center w-full'
						size='lg'
						aria-label='User menu'
					>
						<div className='flex flex-col items-center justify-center text-center flex-grow h-fit p-1 group-data-[state=expanded]:p-2'>
							<Avatar className='h-10 w-10'>
								{typeof userProfile.avatarUrl === "string" ? (
									<AvatarImage
										src={userProfile.avatarUrl}
										alt={displayName || "User avatar"}
									/>
								) : null}
								<AvatarFallback>
									{userProfile.avatarUrl
										? userProfile.avatarUrl
										: displayName?.charAt(0).toUpperCase() || (
												<UserCircle className='h-5 w-5' />
										  )}
								</AvatarFallback>
							</Avatar>
							<span className='sr-only'>User Menu for {displayName}</span>
						</div>
					</SidebarMenuButton>
				</DropdownMenuTrigger>
				<DropdownMenuContent align='end' className='w-60'>
					<DropdownMenuLabel className='font-normal'>
						<div className='flex flex-col space-y-1 p-2'>
							<p className='text-sm font-semibold leading-none truncate'>
								{displayName}
							</p>
							{userProfile.email && (
								<p className='text-xs leading-none text-muted-foreground truncate'>
									{userProfile.email}
								</p>
							)}
						</div>
					</DropdownMenuLabel>
					<DropdownMenuSeparator />
					<DropdownMenuItem onClick={signOut} className='cursor-pointer'>
						<LogOut className='mr-2 h-4 w-4' />
						<span>Logout</span>
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>
		</SidebarMenuItem>
	);
}
