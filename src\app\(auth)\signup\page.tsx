"use client";

import Link from "next/link";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { OAuthButtons } from "@/components/auth/oauth-signin-button";
import { SignUpForm } from "@/components/auth/signup-form"; // Assuming SignUpForm component exists

export default function Signup() {
	return (
		<div>
			<Card className={cn("py-10 min-h-[325px]")}>
				<CardHeader>
					<h2 className='text-lg font-medium text-primary-text'>Sign up</h2>
					<div className='text-sm text-muted-foreground'>
						Already have an account?{" "}
						<Link href='/login' className='text-primary-link hover:underline'>
							Sign in.
						</Link>
					</div>
				</CardHeader>

				<div className='mx-auto border-t border-wd-divider my-6' />
				<CardContent>
					<SignUpForm />

					<div className='flex items-center gap-2 text-divider'>
						<div className='flex-1 border-t border-divider' />
						<span className='text-xs text-divider'>OR</span>
						<div className='flex-1 border-t border-divider' />
					</div>

					<div className='grid grid-cols-3 gap-4'>
						<OAuthButtons provider='slack' operation='login' iconOnly />
						<OAuthButtons provider='github' operation='login' iconOnly />
						<OAuthButtons provider='google' operation='login' iconOnly />
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
