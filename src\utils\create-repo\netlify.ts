import axios from "axios";
import dotenv from "dotenv";

dotenv.config();

async function getNetlifyAccountId(NETLIFY_AUTH_TOKEN: string) {
  try {
    const response = await axios.get(
      "https://api.netlify.com/api/v1/accounts",
      {
        headers: {
          Authorization: `Bearer ${NETLIFY_AUTH_TOKEN}`,
          "Content-Type": "application/json",
        },
      }
    );
    const accountId = response.data[0].id;
    return accountId;
  } catch (error) {
    if (error instanceof Error) {
      throw new Error("Failed to retrieve Netlify account ID.");
    }
    throw new Error("Failed to retrieve Netlify account ID.");
  }
}

async function createEnv(siteId: string, customDomain: string) {
  const NETLIFY_AUTH_TOKEN = process.env.NETLIFY_AUTH_TOKEN as string;
  const accountId = await getNetlifyAccountId(NETLIFY_AUTH_TOKEN);
  await axios.post(
    `https://api.netlify.com/api/v1/accounts/${accountId}/env?site_id=${siteId}`,
    [
      {
        key: "CUSTOM_DOMAIN",
        values: [
          {
            context: "all",
            value: customDomain,
          },
        ],
      },
    ],
    {
      headers: {
        Authorization: `Bearer ${NETLIFY_AUTH_TOKEN}`,
        "Content-Type": "application/json",
      },
    }
  );
}

async function deployToNetlify(siteName: string, repoName: string) {
  if (repoName.endsWith("wd-test-docs")) {
    return null;
  }
  try {
    //remove only the first writedocs- in the beginning of the repo name
    const repositoryName = repoName.replace(/^writedocs-/i, "");

    const domainName = siteName
      .replace(/^-?docs-?/gi, "")
      .replace(/-?docs-?$/gi, "");
    const NETLIFY_AUTH_TOKEN = process.env.NETLIFY_AUTH_TOKEN as string;
    const customDomain = `${domainName}.docs.writedocs.io`;

    const envVariables = {
      CUSTOM_DOMAIN: customDomain,
    };

    const createSiteResponse = await axios.post(
      "https://api.netlify.com/api/v1/sites",
      {
        name: repositoryName,
        custom_domain: customDomain,
        repo: {
          provider: "github",
          repo: `writedocs/${repositoryName}`,
          private: true,
          branch: "main",
          cmd: "npm run build",
          dir: "build",
          installation_id: getNetlifyAccountId(NETLIFY_AUTH_TOKEN),
        },
        build_settings: {
          env: envVariables,
        },
      },
      {
        headers: {
          Authorization: `Bearer ${NETLIFY_AUTH_TOKEN}`,
          "Content-Type": "application/json",
        },
      }
    );

    const siteId = createSiteResponse.data.id;
    await createEnv(siteId, customDomain);

    return createSiteResponse;
  } catch (error) {
    console.error(error);
    throw new Error("Failed deployment to Netlify.");
  }
}

// async function getLatestDeploymentState(siteId) {
//   const NETLIFY_AUTH_TOKEN = process.env.NETLIFY_AUTH_TOKEN;
//   try {
//     const response = await axios.get(
//       `https://api.netlify.com/api/v1/sites/${siteId}/deploys`,
//       {
//         headers: {
//           Authorization: `Bearer ${NETLIFY_AUTH_TOKEN}`,
//         },
//       }
//     );

//     const latestDeployment = response.data[0];
//     if (latestDeployment) {
//       return latestDeployment;
//     } else {
//       throw new Error(`No deployments found for site ${siteId}`);
//     }
//   } catch (error) {
//     console.error(`Error retrieving deployment state: ${error.message}`);
//     throw error;
//   }
// }

async function findNetlifySiteByRepo(repoName: string) {
  try {
    const NETLIFY_AUTH_TOKEN = process.env.NETLIFY_AUTH_TOKEN;
    const response = await axios.get("https://api.netlify.com/api/v1/sites", {
      headers: {
        Authorization: `Bearer ${NETLIFY_AUTH_TOKEN}`,
      },
    });

    const site = response.data.find(
      (site: { build_settings: { repo_path: string } }) =>
        site.build_settings.repo_path === `writedocs/${repoName}`
    );

    if (site) {
      return site;
    } else {
      throw new Error(`Site for repository ${repoName} not found`);
    }
  } catch (error) {
    if (error instanceof Error) {
      console.error(`Error finding site: ${error.message}`);
    }
    throw error;
  }
}

export { deployToNetlify, findNetlifySiteByRepo };
