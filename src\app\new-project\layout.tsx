import {
	<PERSON>,
	CardD<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>er,
	<PERSON><PERSON>itle,
} from "@/components/ui/card";
import Image from "next/image";
import writeDocsLogo from "@/assets/images/logoWriteDocs.svg";
import { createClient } from "@/utils/supabase/server";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { type UserMetadata } from "@supabase/supabase-js";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import SignoutBtn from "@/components/signout-btn";
import { cn } from "@/lib/utils";
import { ChevronLeft } from "lucide-react";

export default async function NewProjectLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	const supabase = createClient();
	const {
		data: {
			user: { user_metadata },
		},
	}: UserMetadata = await supabase.auth.getUser();

	const {
		name = user_metadata.full_name ?? user_metadata.email,
		avatar_url = "",
	} = user_metadata;

	return (
		<div className='flex flex-col items-center justify-center h-screen min-h-fit py-4 bg-page-bg'>
			<Image
				src={writeDocsLogo}
				alt='WriteDocs logo'
				priority
				className='pb-6'
			/>

			<div className='flex flex-col items-center justify-center min-w-full'>
				<Card className={cn("w-full max-w-[380px] h-auto py-8 px-6")}>
					<CardHeader className='pb-5'>
						<div className='flex flex-row justify-between items-center'>
							<div className='flex items-center space-x-3'>
								<Button
									variant='outline'
									size='icon'
									asChild
									className='h-8 w-8'
								>
									<Link href='/'>
										<ChevronLeft className='h-5 w-5' />
										<span className='sr-only'>Back</span>
									</Link>
								</Button>
								<div className='flex items-center space-x-2'>
									<Avatar className='size-[34px]'>
										<AvatarImage src={avatar_url} alt={name + " avatar"} />
										<AvatarFallback>{name[0].toUpperCase()}</AvatarFallback>
									</Avatar>
									<CardTitle className='text-base font-semibold tracking-tight leading-none text-left capitalize'>
										Hello, {name}
									</CardTitle>
								</div>
							</div>
							<SignoutBtn />
						</div>
						<CardDescription className='pt-5 pb-2.5 text-left text-sm font-medium leading-none'>
							Provide the details for your new project
						</CardDescription>
						<Separator />
					</CardHeader>
					{children}
				</Card>
				<p className='text-sm font-semibold p-0 m-0 mt-3 h-auto text-primary-text'>
					Need help?{" "}
					<Button variant='link' className='p-0 m-0 h-auto text-blue-600'>
						<Link
							target='_blank'
							href='https://join.slack.com/t/writedocscommunity/shared_invite/zt-2rxekug4z-CcgqbHYDN0teoxHl8qozVw'
						>
							Contact our team
						</Link>
					</Button>
				</p>
			</div>
		</div>
	);
}
