import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Providers } from "@/components/Providers";
import { SidebarProvider } from "@/components/ui/sidebar";
import { cookies } from "next/headers";

interface DocsBotAIType {
	init: (config: { id: string }) => Promise<void>;
	mount: (config: { id: string }) => Promise<void>;
}

declare global {
	interface Window {
		DocsBotAI: DocsBotAIType;
	}
}

const inter = Inter({
	subsets: ["latin"],
	weight: ["400", "500", "600", "700"],
});

export const metadata: Metadata = {
	title: "Writedocs",
	description: "Writedocs is a platform for writing and sharing documentation.",
};

export default async function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	const cookieStore = await cookies();
	const defaultOpen = cookieStore.get("sidebar_state")?.value === "true";

	return (
		<html lang='en'>
			<body className={`${inter.className} antialiased`}>
				<Providers>
					<SidebarProvider defaultOpen={defaultOpen}>
						{children}
					</SidebarProvider>
				</Providers>
			</body>
		</html>
	);
}
