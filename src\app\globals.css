@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
  --color-custom-test-color: #ff00ff;
  /* Sua cor de teste */

  /* Suas cores WD */
  --color-wd-bg: #F0F0FF;
  --color-wd-border-color: #cad3ff;
  --color-wd-placeholder-color: #7584b7;
  --color-wd-blue: #0029f5;
  --color-wd-blueDark: #001a96;
  --color-wd-font-color: #4a5178;
  --color-wd-primary-btncolor: #000621;
  --color-wd-hover-primary-btncolor: #000d47;
  --color-wd-blue-gray: #e7ebfe;
  /* Note: hover states with @theme are more complex, might need a different approach for hover variants if this doesn't directly create hover:bg-wd-hover-primary-btncolor */

  /* Added from tailwind.config.js for 'wd' section */
  --color-wd-divider: #E8E9FD;
  --color-wd-link: #0029F5;
  --color-wd-page-bg: #F8F8FF;
  --color-wd-input-bg: #F0F2FF;
  --color-wd-input-placeholder: #AAB2D5;
  --color-wd-sso-button-text: #4A5178;
  --color-wd-sso-button-border: #CAD3FF;

  /* Added from tailwind.config.js for 'primary' section */
  --color-primary-text: #4A5178;
  --color-primary-link: #0029F5;
  --color-primary-red-error: #F86467;

  /* text divider */
  --color-divider: #CAD3FF;

  /* Seus Font Sizes */
  --text-headline1: 51px;
  --text-headline1-line-height: 64px;
  --text-headline2: 40px;
  --text-headline2-line-height: 56px;
  --text-headline3: 32px;
  --text-headline3-line-height: 40px;
  --text-headline4: 24px;
  --text-headline4-line-height: 32px;
  --text-headline5: 20px;
  --text-headline5-line-height: 24px;
  --text-subtitle1: 18px;
  /* Use with font-semibold */
  --text-subtitle1-line-height: 24px;
  --text-subtitle2: 18px;
  /* Use with font-normal */
  --text-subtitle2-line-height: 24px;
  --text-body1: 16px;
  --text-body1-line-height: 24px;
  --text-small1: 14px;
  --text-small1-line-height: 24px;
  --text-tiny1: 12px;
  --text-tiny1-line-height: 24px;

  /* Seus Border Radius */
  --radius-btn: 5px;
  --radius-card: 10px;

  /* Seus Spacings */
  --spacing-4_5: 18px;
  /* 18px for card padding, underscore for dot in class name */
  --spacing-nav-py: 8px;
  --spacing-nav-px: 15px;
  --spacing-nav-item-gap: 10px;

  /* Suas cores Neutras */
  --color-neutral-100: #f6f6f6;
  --color-neutral-200: #e7e7e7;
  --color-neutral-300: #b0b0b0;
  --color-neutral-400: #6d6d6d;
  --color-neutral-500: #4f4f4f;
  --color-neutral-600: #3d3d3d;
  --color-neutral-950: #262626;

  /* Suas cores Info */
  --color-info-100: #eef5ff;
  --color-info-200: #dae8ff;
  --color-info-300: #b8d8ff;
  --color-info-400: #8fbfff;
  --color-info-500: #5a9cff;
  --color-info-600: #3778fd;
  --color-info-950: #152156;

  /* Suas cores Tip */
  --color-tip-100: #eefbf3;
  --color-tip-200: #d6f5e0;
  --color-tip-300: #b0eac8;
  --color-tip-400: #7dd8a6;
  --color-tip-500: #48bf82;
  --color-tip-600: #21925b;
  --color-tip-950: #07271a;

  /* Suas cores Warning */
  --color-warning-100: #fff3f1;
  --color-warning-200: #ffe5e0;
  --color-warning-300: #ffcfc6;
  --color-warning-400: #ffae9e;
  --color-warning-500: #ff7f66;
  --color-warning-600: #fd6c4f;
  --color-warning-950: #4a1005;

  /* Suas cores Error */
  --color-error-100: #fff1f1;
  --color-error-200: #ffe0e0;
  --color-error-300: #fec6c6;
  --color-error-400: #fd9d9f;
  --color-error-500: #f86467;
  --color-error-600: #e94d51;
  --color-error-950: #460406;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}


@media (prefers-color-scheme: light) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  font-family: Inter;
  background-color: #f0f0ff !important;

}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(1 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: transparent;
  --sidebar-accent-foreground: oklch(0.5 0.15 250);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}


.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: transparent;
  --sidebar-accent-foreground: oklch(0.87 0.06 258.5);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}