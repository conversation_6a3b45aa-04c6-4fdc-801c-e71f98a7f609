"use client";
import { OnboardingForm } from "@/components/OnboardingForm";
import { InvitesList } from "@/components/InvitesList";
import { useState, useEffect } from "react";

export default function Onboarding() {
  const [showInvites, setShowInvites] = useState(false);
  const [inviteCount, setInviteCount] = useState(0);

  const handleInvitesChange = (count: number) => {
    if (count !== inviteCount) {
      setInviteCount(count);
    }
  };

  // Efeito para verificar convites iniciais
  useEffect(() => {
    async function checkInvites() {
      try {
        const res = await fetch("/api/my-invites");
        const data = await res.json();
        const count = (data.invites || []).length;
        setInviteCount(count);
        if (count > 0) {
          setShowInvites(true);
        }
      } catch (error) {
        console.error("Error checking invites:", error);
      }
    }

    checkInvites();
  }, []);

  return (
    <div className="container max-w-3xl">
      <OnboardingForm
        hasInvites={inviteCount > 0}
        onShowInvites={() => setShowInvites(true)}
      />
      <InvitesList
        variant="modal"
        open={showInvites}
        onOpenChange={setShowInvites}
        onInvitesChange={handleInvitesChange}
      />
    </div>
  );
}
