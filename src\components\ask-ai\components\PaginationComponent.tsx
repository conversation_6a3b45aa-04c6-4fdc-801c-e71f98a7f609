import { ChevronLeft, ChevronRight } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import {
	Pagination,
	PaginationContent,
	PaginationItem,
	PaginationLink,
} from "@/components/ui/pagination";
import { DocsBotPagination } from "@/app/(home)/[projectId]/ask-ai/types";

interface PaginationComponentProps {
	pagination: DocsBotPagination | null;
	currentPage: number;
	onPageChange: (pageNumber: number) => void;
	isLoading: boolean;
}

export const PaginationComponent = ({
	pagination,
	currentPage,
	onPageChange,
	isLoading,
}: PaginationComponentProps) => {
	if (!pagination) return null;

	const { hasMorePages, totalCount, perPage } = pagination;
	const page = currentPage;

	if (totalCount <= perPage) return null;

	// Calculate total pages
	const totalPages = Math.ceil(totalCount / perPage);

	// Create an array of page numbers to show
	let pageNumbers: number[] = [];
	const maxPageButtons = 3;

	if (totalPages <= maxPageButtons) {
		// If we have fewer pages than our max, show all pages
		pageNumbers = Array.from({ length: totalPages }, (_, i) => i);
	} else {
		// Create a sliding window of pages centered around the current page
		const halfMaxButtons = Math.floor(maxPageButtons / 2);
		let startPage = Math.max(0, page - halfMaxButtons);
		const endPage = Math.min(totalPages - 1, startPage + maxPageButtons - 1);

		// Adjust startPage if we're near the end
		startPage = Math.max(0, endPage - maxPageButtons + 1);

		pageNumbers = Array.from(
			{ length: endPage - startPage + 1 },
			(_, i) => startPage + i
		);
	}

	if (isLoading) {
		return (
			<div className='flex space-x-1'>
				{Array.from({ length: 3 }).map((_, index) => (
					<Skeleton key={index} className='h-8 w-8 rounded' />
				))}
			</div>
		);
	}

	return (
		<Pagination>
			<PaginationContent>
				<PaginationItem>
					<PaginationLink
						href='#'
						onClick={(e) => {
							e.preventDefault();
							onPageChange(Math.max(0, page - 1));
						}}
						className={`h-8 w-8 p-0 flex items-center justify-center ${
							page === 0 || isLoading ? "pointer-events-none opacity-50" : ""
						}`}
					>
						<span className='sr-only'>Previous</span>
						<ChevronLeft
							className={
								page === 0 ? "text-muted-foreground" : "text-wd-font-color"
							}
							size={16}
						/>
					</PaginationLink>
				</PaginationItem>

				{pageNumbers.map((number) => (
					<PaginationItem key={number}>
						<PaginationLink
							href='#'
							onClick={(e) => {
								e.preventDefault();
								onPageChange(number);
							}}
							isActive={page === number}
							className={`h-8 w-8 ${
								isLoading ? "pointer-events-none opacity-50" : ""
							} ${
								page === number
									? "bg-blue-500 text-white hover:bg-blue-600"
									: ""
							}`}
						>
							{number + 1}
						</PaginationLink>
					</PaginationItem>
				))}

				<PaginationItem>
					<PaginationLink
						href='#'
						onClick={(e) => {
							e.preventDefault();
							onPageChange(page + 1);
						}}
						className={`h-8 w-8 p-0 flex items-center justify-center ${
							!hasMorePages || isLoading ? "pointer-events-none opacity-50" : ""
						}`}
					>
						<span className='sr-only'>Next</span>
						<ChevronRight
							className={
								!hasMorePages ? "text-muted-foreground" : "text-wd-font-color"
							}
							size={16}
						/>
					</PaginationLink>
				</PaginationItem>
			</PaginationContent>
		</Pagination>
	);
};
