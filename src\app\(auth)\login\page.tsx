"use client";

import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { OAuthButtons } from "@/components/auth/oauth-signin-button";
import { LoginForm } from "@/components/auth/login-form";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

export default function Login() {
	const [ssoModeActive, setSsoModeActive] = useState(false);
	const [isLoadingSso, setIsLoadingSso] = useState(false);
	const [ssoError, setSsoError] = useState<string | null>(null);

	const handleBackToNormalLogin = () => {
		setSsoModeActive(false);
		setSsoError(null);
	};

	return (
		<div>
			<Card className={cn("py-10 h-fit w-[380px]")}>
				<CardHeader className={cn(ssoModeActive && "flex-row")}>
					<div className='flex items-center gap-2'>
						{ssoModeActive && (
							<Button
								variant='ghost'
								size='icon'
								className='text-muted-foreground hover:text-primary-text'
								onClick={handleBackToNormalLogin}
								disabled={isLoadingSso}
								aria-label='Back to normal login'
							>
								<ArrowLeft className='h-5 w-5' />
							</Button>
						)}
						<h2
							className={cn(
								"text-lg font-medium text-primary-text",
								ssoModeActive && "flex-1 text-center"
							)}
						>
							{ssoModeActive
								? "Enterprise Single Sign-On"
								: "Log in to WriteDocs"}
						</h2>
					</div>
					{!ssoModeActive && (
						<div className='text-sm text-muted-foreground'>
							Don&apos;t have an account?{" "}
							<Link
								href='/signup'
								className='text-primary-link hover:underline'
							>
								Sign up.
							</Link>
						</div>
					)}
				</CardHeader>

				{!ssoModeActive && (
					<div className='mx-auto border-t border-wd-divider my-6' />
				)}

				<CardContent className={cn(ssoModeActive && "pt-6")}>
					<LoginForm
						ssoModeActive={ssoModeActive}
						setSsoModeActive={setSsoModeActive}
						isLoadingSso={isLoadingSso}
						setIsLoadingSso={setIsLoadingSso}
						ssoError={ssoError}
						setSsoError={setSsoError}
					/>

					<div className='flex items-center gap-2 text-divider'>
						<div className='flex-1 border-t border-divider' />
						<span className='text-xs text-divider'>OR</span>
						<div className='flex-1 border-t border-divider' />
					</div>

					<div className='grid grid-cols-3 gap-2'>
						<OAuthButtons provider='slack' operation='login' iconOnly />
						<OAuthButtons provider='github' operation='login' iconOnly />
						<OAuthButtons provider='google' operation='login' iconOnly />
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
