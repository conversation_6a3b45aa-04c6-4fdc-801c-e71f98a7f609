// Generated by Wrangler
// by running `wrangler types --env-interface CloudflareEnv env.d.ts`

interface CloudflareEnv {
  GITHUB_APP_ID: string;
  NETLIFY_AUTH_TOKEN: string;
  NEXT_PUBLIC_SUPABASE_URL: string;
  NEXT_PUBLIC_SUPABASE_ANON_KEY: string;
  NEXT_SUPABASE_SERVICE_ROLE_KEY: string;
  RESEND_API_KEY: string;
  SITE_URL: string;
  DOCSBOT_API_KEY: string;
  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: string;
  STRIPE_WEBHOOK_SECRET: string;
  STRIPE_SECRET_KEY: string;
}
