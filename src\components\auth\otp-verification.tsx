"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { createClient } from "@/utils/supabase/client";
import { useToast } from "@/components/ToastProvider";
import { cn } from "@/lib/utils";
import { ArrowLeft } from "lucide-react";

interface OTPVerificationProps {
  email: string;
  onBack: () => void;
  type?: "signup" | "recovery";
}

export function OTPVerification({
  email,
  onBack,
  type = "signup",
}: OTPVerificationProps) {
  const [isVerifying, setIsVerifying] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [countdown, setCountdown] = useState(120);
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const { addToast } = useToast();
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const handleChange = (index: number, value: string) => {
    const numericValue = value.replace(/[^\d]/g, ""); // Remove todos os não-dígitos

    if (numericValue.length > 1 && value.length > 1) {
      // Conteúdo colado com múltiplos dígitos
      const newOtp = [...otp];
      const digitsToPaste = numericValue.split("");
      let currentOtpIndex = index;
      let pastedCount = 0;

      for (const digit of digitsToPaste) {
        if (currentOtpIndex < 6) {
          newOtp[currentOtpIndex] = digit;
          currentOtpIndex++;
          pastedCount++;
        } else {
          break;
        }
      }
      setOtp(newOtp);

      const focusIndex = Math.min(index + pastedCount, 5);
      const nextInput = document.querySelector(
        `input[name="otp-${focusIndex}"]`
      ) as HTMLInputElement;

      if (nextInput) {
        if (
          newOtp[focusIndex] &&
          focusIndex < 5 &&
          newOtp.slice(index, index + pastedCount).length === pastedCount
        ) {
          // Se o campo focado está preenchido pela colagem E não é o último E a colagem foi completa,
          // tenta focar o próximo após o último colado.
          const inputAfterPasted = document.querySelector(
            `input[name="otp-${Math.min(index + pastedCount, 5)}"]` // Garante que não ultrapasse o último campo
          ) as HTMLInputElement;
          if (inputAfterPasted) inputAfterPasted.focus();
          // Se o campo focado (que recebeu o último digito colado) não for o último campo total,
          // e ele estiver preenchido, avançar o foco para o próximo campo.
          if (focusIndex < 5 && newOtp[focusIndex]) {
            const nextField = document.querySelector(
              `input[name="otp-${focusIndex + 1}"]`
            ) as HTMLInputElement;
            if (nextField) nextField.focus();
          } else {
            nextInput.focus(); // Foca o campo atual se for o último ou se o próximo não precisar de foco.
          }
        } else {
          nextInput.focus(); // Foca o campo atual (último afetado pela colagem ou o próximo vazio)
        }
      }
      return;
    }

    // Input de um único caractere (ou string vazia se um não-dígito foi digitado e filtrado)
    if (numericValue.length <= 1) {
      const newOtp = [...otp];
      newOtp[index] = numericValue; // Atribui o único dígito ou string vazia
      setOtp(newOtp);

      if (numericValue && index < 5) {
        // Se um dígito foi inserido e não é o último input
        const nextInput = document.querySelector(
          `input[name="otp-${index + 1}"]`
        ) as HTMLInputElement;
        if (nextInput) nextInput.focus();
      }
    }
  };

  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      const prevInput = document.querySelector(
        `input[name="otp-${index - 1}"]`
      ) as HTMLInputElement;
      if (prevInput) prevInput.focus();
    }
  };

  const handleVerify = async () => {
    const code = otp.join("");
    if (code.length !== 6) {
      addToast("Please enter the complete verification code", "error");
      return;
    }
    setIsVerifying(true);
    try {
      const { error } = await supabase.auth.verifyOtp({
        email,
        token: code,
        type: type === "signup" ? "signup" : "email",
      });
      if (error) throw error;
      if (type === "signup") {
        addToast("Email verified successfully! You can now log in.", "success");
        onBack();
      } else {
        addToast("Code verified successfully!", "success");
        router.push(`/reset-password?email=${email}&code=${code}`);
      }
      router.push("/onboarding");
    } catch (error) {
      addToast(
        error instanceof Error ? error.message : "Verification failed",
        "error"
      );
    } finally {
      setIsVerifying(false);
    }
  };

  const handleResend = async () => {
    setIsSending(true);
    try {
      let error;
      if (type === "signup") {
        const { error: resendError } = await supabase.auth.resend({
          type: "signup",
          email: email,
        });
        error = resendError;
      } else {
        const { error: resetError } = await supabase.auth.resetPasswordForEmail(
          email,
          {}
        );
        error = resetError;
      }
      if (error) throw error;
      addToast("Verification code resent successfully", "success");
      setCountdown(120);
    } catch (error) {
      addToast(
        error instanceof Error ? error.message : "Failed to resend code",
        "error"
      );
    } finally {
      setIsSending(false);
    }
  };

  return (
    <Card className="w-full max-w-md shadow-xl rounded-lg">
      <CardHeader className="p-6 pb-3">
        <div className="relative flex items-center justify-center text-center">
          <Button
            variant="ghost"
            size="icon"
            onClick={onBack}
            className="absolute left-0 top-1/2 -translate-y-1/2 text-muted-foreground hover:bg-accent hover:text-accent-foreground h-auto p-0"
            aria-label="Back"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>

          <h2 className="text-xl font-semibold text-primary-text">
            Verify your email
          </h2>
        </div>
      </CardHeader>
      <div className="-mx-7 border-t border-border" />
      <CardContent className="px-6 pb-6 pt-4 text-center">
        <p className="text-sm text-muted-foreground">
          We sent a verification code to{" "}
          <span className="font-medium text-primary-text">{email}</span>
        </p>
        <div className="grid grid-cols-6 gap-3 mt-2 mb-2">
          {otp.map((digit, index) => (
            <Input
              key={index}
              type="text"
              name={`otp-${index}`}
              value={digit}
              onChange={(e) => handleChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              className="w-full h-14 text-center text-3xl font-medium px-0 rounded-md"
              maxLength={1}
              inputMode="numeric"
              pattern="\d*"
              aria-label={`OTP digit ${index + 1}`}
              disabled={isVerifying}
            />
          ))}
        </div>
        <p className="text-xs text-muted-foreground text-center ">
          Didn&apos;t receive the code? Check your spam/junk folder, or try
          resending after the timer.
        </p>
        <div className="text-center mb-2">
          <button
            onClick={handleResend}
            disabled={isSending || countdown > 0}
            className={cn(
              "text-sm text-primary-link hover:underline disabled:text-muted-foreground",
              (isSending || countdown > 0) && "cursor-not-allowed opacity-70"
            )}
          >
            {isSending
              ? "Sending..."
              : countdown > 0
              ? `Resend code in ${formatTime(countdown)}`
              : "Resend code"}
          </button>
        </div>
        <Button
          className="w-full h-12 bg-wd-blue hover:bg-wd-blueDark text-base font-medium rounded-md"
          onClick={handleVerify}
          disabled={isVerifying || otp.join("").length !== 6}
        >
          {isVerifying ? "Verifying..." : "Verify Email"}
        </Button>
      </CardContent>
    </Card>
  );
}
