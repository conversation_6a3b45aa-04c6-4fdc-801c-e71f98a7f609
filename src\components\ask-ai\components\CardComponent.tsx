import { format } from "date-fns";
import { BotQuestion } from "@/app/(home)/[projectId]/ask-ai/types";
import { CalendarIcon } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { RatingDisplay } from "./RatingDisplay";
import { AnswerStatusDisplay } from "./AnswerStatusDisplay";
import { SourcesDisplay } from "./SourcesDisplay";

interface CardComponentProps {
	questions: BotQuestion[];
	isLoading: boolean;
	onQuestionClick: (question: BotQuestion) => void;
}

export const CardComponent = ({
	questions,
	isLoading,
	onQuestionClick,
}: CardComponentProps) => {
	// Format date to a readable format MM/dd/yyyy, HH:mm:ss
	const formatDate = (dateString: string) => {
		try {
			return format(new Date(dateString), "MM/dd/yyyy, HH:mm:ss");
		} catch {
			return dateString;
		}
	};

	if (isLoading) {
		return (
			<div className='space-y-4'>
				{Array.from({ length: 3 }).map((_, index) => (
					<div
						key={index}
						className='bg-white rounded-lg border border-gray-200 overflow-hidden p-4'
					>
						<div className='flex justify-end mb-2'>
							<div className='flex items-center'>
								<CalendarIcon size={12} className='text-muted mr-1' />
								<Skeleton className='h-3 w-24' />
							</div>
						</div>

						<Skeleton className='h-4 w-full mb-2' />
						<Skeleton className='h-4 w-3/4 mb-3' />
						<Skeleton className='h-4 w-full mb-1' />
						<Skeleton className='h-4 w-5/6' />

						<div className='flex justify-between items-center pt-3 mt-2 border-t border-gray-100'>
							<div className='flex space-x-4'>
								<Skeleton className='h-5 w-16' />
								<Skeleton className='h-5 w-16' />
							</div>
							<Skeleton className='h-5 w-20' />
						</div>
					</div>
				))}
			</div>
		);
	}

	if (questions.length === 0) {
		return (
			<div className='flex flex-col items-center justify-center py-12 px-4 bg-white rounded-lg border border-wd-border-color shadow-sm mt-4'>
				<div className='text-muted-foreground mb-2'>
					<svg
						xmlns='http://www.w3.org/2000/svg'
						width='40'
						height='40'
						viewBox='0 0 24 24'
						fill='none'
						stroke='currentColor'
						strokeWidth='2'
						strokeLinecap='round'
						strokeLinejoin='round'
					>
						<circle cx='12' cy='12' r='10'></circle>
						<line x1='8' y1='12' x2='16' y2='12'></line>
					</svg>
				</div>
				<h3 className='text-lg font-medium text-wd-font-color mb-1'>
					No results
				</h3>
				<p className='text-muted-foreground text-center'>
					Try adjusting your filters to see more results.
				</p>
			</div>
		);
	}

	return (
		<div className='space-y-4 sm:grid sm:grid-cols-2 sm:gap-4 sm:space-y-0'>
			{questions.map((question) => (
				<div
					key={question.id}
					className='bg-white rounded-lg border border-wd-border-color overflow-hidden p-4 hover:shadow-md transition-shadow cursor-pointer flex flex-col'
					onClick={() => onQuestionClick(question)}
				>
					<div className='flex flex-col justify-end'>
						<div className='text-sm font-medium text-wd-font-color mb-1'>
							{question.standaloneQuestion || question.question}
						</div>
						<div className='text-sm text-wd-font-color/80 mb-3 line-clamp-2'>
							{question.answer}
						</div>

						<div className='flex justify-between items-center text-xs text-muted-foreground pt-2 border-t border-wd-border-color'>
							<div className='flex flex-col xs:flex-row xs:space-x-4 space-y-2 xs:space-y-0'>
								<div className='flex items-center'>
									<span className='mr-1'>Sources:</span>
									<SourcesDisplay sources={question.sources || []} />
								</div>
								<div className='flex items-center'>
									<span className='mr-1'>Rating:</span>
									<div>
										<RatingDisplay
											rating={question.rating}
											escalation={question.escalation}
											size={18}
										/>
									</div>
								</div>
							</div>
							<div className='flex items-center'>
								<span className='mr-1'>Answered:</span>
								<div>
									<AnswerStatusDisplay
										couldAnswer={question.couldAnswer}
										size={18}
									/>
								</div>
							</div>
						</div>
					</div>
					<div className='text-xs text-muted-foreground flex items-center justify-end mt-2'>
						<CalendarIcon size={12} className='mr-1' />
						{formatDate(question.createdAt)}
					</div>
				</div>
			))}
		</div>
	);
};
