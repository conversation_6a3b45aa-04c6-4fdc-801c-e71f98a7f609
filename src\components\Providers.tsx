"use client";

import { ToastProvider } from "./ToastProvider";
import { TooltipProvider } from "@/components/ui/tooltip";
import { CombinedContextProvider } from "@/contexts";

export function Providers({ children }: { children: React.ReactNode }) {
	return (
		<CombinedContextProvider>
			{" "}
			<TooltipProvider delayDuration={100} disableHoverableContent={false}>
				<ToastProvider>{children}</ToastProvider>
			</TooltipProvider>
		</CombinedContextProvider>
	);
}
