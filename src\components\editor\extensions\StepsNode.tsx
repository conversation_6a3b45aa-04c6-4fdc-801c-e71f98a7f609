import { Node } from "@tiptap/core";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ReactNodeViewRenderer } from "@tiptap/react";

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    steps: {
      /**
       * Insert steps with the specified attributes.
       */
      setSteps: (attributes?: StepsAttrs) => ReturnType;
    };
  }
}

import React, { useState, useCallback, useEffect } from "react";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ToastProvider";
import type { NodeViewProps } from "@tiptap/react";

// Import types and utilities
import { StepData, StepsAttrs } from "./steps/types";
import { generateDefaultSteps, getTitleSizeForLevel } from "./steps/utils";
import { injectStyles } from "./steps/styles";
import { StepsList } from "./steps/components/StepItem";
import hljs from "highlight.js/lib/core";

// Import only the languages we need to keep bundle size small
import javascript from "highlight.js/lib/languages/javascript";
import typescript from "highlight.js/lib/languages/typescript";
import python from "highlight.js/lib/languages/python";
import java from "highlight.js/lib/languages/java";
import xml from "highlight.js/lib/languages/xml"; // for HTML
import css from "highlight.js/lib/languages/css";
import json from "highlight.js/lib/languages/json";
import sql from "highlight.js/lib/languages/sql";
import bash from "highlight.js/lib/languages/bash";
import php from "highlight.js/lib/languages/php";
import go from "highlight.js/lib/languages/go";
import rust from "highlight.js/lib/languages/rust";
import cpp from "highlight.js/lib/languages/cpp";
import csharp from "highlight.js/lib/languages/csharp";
import yaml from "highlight.js/lib/languages/yaml";
import markdown from "highlight.js/lib/languages/markdown";
import dockerfile from "highlight.js/lib/languages/dockerfile";

// Register languages
hljs.registerLanguage("javascript", javascript);
hljs.registerLanguage("typescript", typescript);
hljs.registerLanguage("python", python);
hljs.registerLanguage("java", java);
hljs.registerLanguage("html", xml);
hljs.registerLanguage("xml", xml);
hljs.registerLanguage("css", css);
hljs.registerLanguage("json", json);
hljs.registerLanguage("sql", sql);
hljs.registerLanguage("bash", bash);
hljs.registerLanguage("php", php);
hljs.registerLanguage("go", go);
hljs.registerLanguage("rust", rust);
hljs.registerLanguage("cpp", cpp);
hljs.registerLanguage("csharp", csharp);
hljs.registerLanguage("yaml", yaml);
hljs.registerLanguage("markdown", markdown);
hljs.registerLanguage("docker", dockerfile);

// Inject styles on module load
injectStyles();

// ============================================================================
// Main Steps Component
// ============================================================================

// Função para normalizar titleSizes - todos os steps no mesmo nível devem ter o mesmo heading
const normalizeStepTitleSizes = (steps: StepData[]): StepData[] => {
  if (!steps || steps.length === 0) return steps;

  // Para o nível raiz, usar o titleSize do primeiro step
  const rootTitleSize = steps[0].titleSize;

  const normalizeLevel = (
    stepsAtLevel: StepData[],
    levelTitleSize: string
  ): StepData[] => {
    return stepsAtLevel.map((step) => {
      const normalizedStep = {
        ...step,
        titleSize: levelTitleSize as "h2" | "h3" | "h4" | "h5" | "h6",
      };

      // Recursivamente normalizar sub-steps
      if (step.subSteps && step.subSteps.length > 0) {
        const subStepTitleSize = step.subSteps[0].titleSize;
        normalizedStep.subSteps = normalizeLevel(
          step.subSteps,
          subStepTitleSize
        );
      }

      return normalizedStep;
    });
  };

  return normalizeLevel(steps, rootTitleSize);
};

const StepsComponent: React.FC<NodeViewProps> = ({
  node,
  updateAttributes,
  selected,
}) => {
  const [steps, setSteps] = useState<StepData[]>(() => {
    try {
      const nodeSteps = node?.attrs?.steps;

      if (!Array.isArray(nodeSteps)) {
        console.warn("Invalid node steps, using defaults:", nodeSteps);
        return generateDefaultSteps();
      }

      const validSteps = nodeSteps.filter((step) => {
        return (
          step &&
          typeof step === "object" &&
          step !== null &&
          "id" in step &&
          typeof step.id === "string"
        );
      }) as StepData[];

      if (validSteps.length === 0) {
        console.warn("No valid steps found, using defaults");
        return generateDefaultSteps();
      }

      // Normalizar titleSizes para garantir consistência
      return normalizeStepTitleSizes(validSteps);
    } catch (error) {
      console.error("Error initializing steps:", error);
      return generateDefaultSteps();
    }
  });
  const [editingStepId, setEditingStepId] = useState<string | null>(null);
  const { addToast } = useToast();
  const contentRef = React.useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!contentRef.current) return;

    const highlightCode = () => {
      if (contentRef.current) {
        const codeBlocks = contentRef.current.querySelectorAll(
          "pre code:not(.hljs)"
        );
        codeBlocks.forEach((block) => {
          hljs.highlightElement(block as HTMLElement);
        });
      }
    };

    // Highlight on initial render and when steps change
    highlightCode();

    const observer = new MutationObserver(() => {
      // We can be more specific here, but for now, let's re-highlight on any change.
      highlightCode();
    });

    observer.observe(contentRef.current, {
      childList: true,
      subtree: true,
      characterData: true,
    });

    return () => {
      observer.disconnect();
    };
  }, [steps, editingStepId]);

  // Auto-save com debounce quando os steps mudam
  useEffect(() => {
    if (steps.length > 0) {
      const timeoutId = setTimeout(() => {
        console.log("🔧 StepsNode auto-saving steps:", steps);
        updateAttributes({ steps });
      }, 300);

      return () => clearTimeout(timeoutId);
    }
  }, [steps, updateAttributes]);

  // Helper function to find and update a step anywhere in the tree
  const updateStepInTree = useCallback(
    (
      steps: StepData[],
      stepId: string,
      updater: (step: StepData) => StepData | null
    ): StepData[] => {
      return steps
        .map((step) => {
          if (step.id === stepId) {
            const updated = updater(step);
            return updated || step;
          }
          if (step.subSteps) {
            return {
              ...step,
              subSteps: updateStepInTree(step.subSteps, stepId, updater),
            };
          }
          return step;
        })
        .filter(Boolean) as StepData[];
    },
    []
  );

  const handleEdit = useCallback((step: StepData) => {
    setEditingStepId(step.id);
  }, []);

  const handleSaveEdit = useCallback(
    (updatedStep: StepData) => {
      setSteps((prev) => {
        const newSteps = updateStepInTree(
          prev,
          updatedStep.id,
          () => updatedStep
        );
        // Aplicar normalização para manter consistência de titleSizes
        return normalizeStepTitleSizes(newSteps);
      });
      setEditingStepId(null);
      addToast("Step updated successfully", "success");
    },
    [updateStepInTree, addToast]
  );

  const handleCancelEdit = useCallback(() => {
    setEditingStepId(null);
  }, []);

  const handleDelete = useCallback(
    (stepId: string) => {
      let hasSubSteps = false;
      const checkSubSteps = (steps: StepData[]) => {
        for (const step of steps) {
          if (step.id === stepId && step.subSteps && step.subSteps.length > 0) {
            hasSubSteps = true;
            return;
          }
          if (step.subSteps) {
            checkSubSteps(step.subSteps);
          }
        }
      };
      checkSubSteps(steps);

      if (hasSubSteps) {
        if (
          !confirm(
            "This step has sub-steps. Delete the step and all its sub-steps?"
          )
        ) {
          return;
        }
      }

      const removeStep = (steps: StepData[]): StepData[] => {
        return steps
          .filter((step) => step.id !== stepId)
          .map((step) => ({
            ...step,
            subSteps: step.subSteps ? removeStep(step.subSteps) : [],
          }));
      };

      setSteps((prev) => {
        const newSteps = removeStep(prev);
        return newSteps;
      });
      addToast("Step deleted successfully", "info");
    },
    [steps, addToast]
  );

  const handleAddSubStep = useCallback(
    (parentId: string) => {
      let parentLevel = -1;
      let parentStep: StepData | null = null;

      const findParentStep = (
        steps: StepData[],
        targetId: string,
        currentLevel: number
      ): void => {
        for (const step of steps) {
          if (step.id === targetId) {
            parentLevel = currentLevel;
            parentStep = step;
            return;
          }
          if (step.subSteps) {
            findParentStep(step.subSteps, targetId, currentLevel + 1);
          }
        }
      };
      findParentStep(steps, parentId, 0);

      // Determinar titleSize baseado no primeiro sub-step existente ou calcular novo nível
      let newTitleSize: "h2" | "h3" | "h4" | "h5" | "h6";
      if (parentStep && parentStep.subSteps && parentStep.subSteps.length > 0) {
        // Se já existem sub-steps, usar o titleSize do primeiro
        newTitleSize = parentStep.subSteps[0].titleSize;
      } else {
        // Se não existem sub-steps, calcular baseado no nível do parent
        newTitleSize = getTitleSizeForLevel(parentLevel + 1);
      }

      const newSubStep: StepData = {
        id: Date.now().toString(),
        title: "New Sub-step",
        content: "<p>Content for the new sub-step.</p>",
        titleSize: newTitleSize,
        subSteps: [],
      };

      setSteps((prev) => {
        const newSteps = updateStepInTree(prev, parentId, (step) => ({
          ...step,
          subSteps: [...(step.subSteps || []), newSubStep],
        }));
        return newSteps;
      });

      setEditingStepId(newSubStep.id);
      addToast("New sub-step added", "success");
    },
    [steps, updateStepInTree, addToast]
  );

  const handleAddRootStep = useCallback(() => {
    // Determinar o titleSize baseado no primeiro step existente
    const firstStepTitleSize = steps.length > 0 ? steps[0].titleSize : "h3";

    const newStep: StepData = {
      id: Date.now().toString(),
      title: "New Step",
      content: "<p>Content for the new step.</p>",
      titleSize: firstStepTitleSize, // Usar o mesmo titleSize do primeiro step
      subSteps: [],
    };
    setSteps((prev) => {
      const newSteps = [...prev, newStep];
      return newSteps;
    });
    setEditingStepId(newStep.id);
    addToast("New step added", "success");
  }, [addToast, steps]);

  return (
    <NodeViewWrapper
      className="steps-node w-full border-dashed border-2 border-gray-300 dark:border-gray-600 rounded-lg hover:border-blue-400/50 dark:hover:border-blue-600/50 transition-colors duration-200"
      draggable={false}
      contentEditable={false}
      data-drag-handle=""
    >
      <div
        ref={contentRef}
        className={`steps-node-container  p-2 bg-white dark:bg-gray-50 relative w-full group ${
          selected ? "ring-2 ring-blue-400/50 rounded-lg" : ""
        }`}
        draggable={false}
      >
        {/* Steps List */}
        {steps.length > 0 ? (
          <StepsList
            steps={steps}
            level={0}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onAddSubStep={handleAddSubStep}
            editingStepId={editingStepId}
            onSaveEdit={handleSaveEdit}
            onCancelEdit={handleCancelEdit}
          />
        ) : (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            No steps yet. Click &quot;Add Step&quot; to get started.
          </div>
        )}

        {/* Add Step Button - positioned at the bottom */}
        <div className="flex justify-center mt-4">
          <Button
            onClick={handleAddRootStep}
            size="sm"
            variant="outline"
            className="h-8 px-3 text-xs border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:border-blue-400 hover:text-blue-600 dark:hover:border-blue-500 dark:hover:text-blue-400 opacity-0 group-hover:opacity-100 transition-all duration-200 bg-transparent hover:bg-blue-50 dark:hover:bg-blue-950/20"
          >
            <Plus className="w-3 h-3 mr-1" />
            Add Step
          </Button>
        </div>
      </div>
    </NodeViewWrapper>
  );
};

// ============================================================================
// Tiptap Extension
// ============================================================================

export const StepsNode = Node.create({
  name: "steps",
  group: "block",
  content: "",
  atom: true,
  draggable: false,
  selectable: true,

  addAttributes() {
    return {
      steps: {
        default: [],
        parseHTML: (element) => {
          try {
            // Tentar pegar de data-steps primeiro, depois de steps
            const stepsData =
              element.getAttribute("data-steps") ||
              element.getAttribute("steps");
            if (stepsData) {
              const parsed = JSON.parse(stepsData);
              console.log("🔧 StepsNode parseHTML parsed steps:", parsed);
              return parsed;
            }

            // Se não tiver dados nos atributos, tentar extrair dos elementos filhos
            const stepElements = element.querySelectorAll("h2, h3, h4, h5, h6");
            if (stepElements.length > 0) {
              return Array.from(stepElements).map((stepEl, index) => ({
                id: Date.now().toString() + index,
                title: stepEl.textContent || `Step ${index + 1}`,
                content: "<p>Step content</p>",
                titleSize: stepEl.tagName.toLowerCase(),
                subSteps: [],
              }));
            }

            return [];
          } catch (error) {
            console.error("Error parsing steps data:", error);
            return [];
          }
        },
        renderHTML: (attributes) => {
          if (!attributes.steps || !Array.isArray(attributes.steps)) {
            console.log(
              "🔧 StepsNode renderHTML no steps, returning empty array"
            );
            return { "data-steps": "[]" };
          }
          console.log("🔧 StepsNode renderHTML with steps:", attributes.steps);
          return { "data-steps": JSON.stringify(attributes.steps) };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: "Steps",
      },
      {
        tag: 'div[data-type="steps"]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    const steps = HTMLAttributes.steps || HTMLAttributes["data-steps"] || [];
    let stepsList: StepData[] = [];
    if (typeof steps === "string") {
      try {
        stepsList = JSON.parse(steps);
      } catch {
        stepsList = [];
      }
    } else if (Array.isArray(steps)) {
      stepsList = steps;
    }

    const renderStep = (step: StepData): any[] => {
      const titleEl = [step.titleSize || "h3", {}, step.title || ""];
      const contentEl = step.content ? ["div", {}, step.content] : null;

      let subStepsEl = null;
      if (step.subSteps && step.subSteps.length > 0) {
        const subStepItems = step.subSteps.map(renderStep).flat();
        subStepsEl = ["Steps", {}, ...subStepItems];
      }

      const result = [titleEl];
      if (contentEl) result.push(contentEl);
      if (subStepsEl) result.push(subStepsEl);
      return result;
    };

    const stepElements = stepsList.map(renderStep).flat();

    return [
      "div",
      {
        "data-type": "steps",
        "data-steps": JSON.stringify(stepsList),
        class: "steps-node",
      },
      ...stepElements,
    ];
  },

  addNodeView() {
    return ReactNodeViewRenderer(StepsComponent);
  },

  addCommands() {
    return {
      setSteps:
        (attributes?: StepsAttrs) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: attributes || { steps: generateDefaultSteps() },
          });
        },
    };
  },
});

export default StepsNode;
