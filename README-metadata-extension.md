# Metadata Extension for Tiptap

This custom Tiptap extension allows you to visualize and edit page metadata directly in the editor, preventing users from accidentally deleting this important information.

## Features

✅ **Metadata Visualization**: Displays title, description, and slug in an elegant interface
✅ **Inline Editing**: Edit metadata directly within the editor
✅ **Complete Deletion Protection**: Multiple layers of protection prevent accidental deletion
✅ **Automatic Detection**: Automatically detects existing metadata in HTML
✅ **Responsive Interface**: Adaptive design for desktop and mobile
✅ **Dark Mode Support**: Fully compatible with dark theme
✅ **Visual Indicators**: Visual badges indicate the component is protected
✅ **Interactive Tooltips**: Helpful tips explain each field and SEO best practices

## Protection System Implemented

### 🛡️ Multiple Protection Layers

The extension implements several protection layers to ensure metadata cannot be accidentally deleted:

1. **Keyboard Protection**:

   - Intercepts `Backspace` and `Delete` when cursor is in the metadata node
   - Prevents `Ctrl+A` from selecting the metadata node with the rest of the content

2. **Transaction Protection**:

   - ProseMirror plugin that blocks any transaction attempting to remove the metadata node
   - Logs deletion attempts to console for debugging

3. **Visual Protection**:

   - `draggable: false` prevents the node from being dragged
   - `contentEditable={false}` in NodeViewWrapper
   - `user-select: none` in CSS prevents text selection

4. **Visual Indicators**:
   - "Protected" badge in view mode
   - "Editing" badge in edit mode
   - Pulse animation to draw attention
   - Subtle hover effect with gradient

### 🎯 Protection Behaviors

- **Backspace/Delete**: Moves cursor outside the node instead of deleting
- **Ctrl+A**: Selects only editable content (skips metadata)
- **Drag & Drop**: Completely disabled for the metadata node
- **Selection**: Node can be selected but not deleted

## How It Works

### 1. Automatic Detection

When a document with HTML metadata is loaded, the extension automatically detects and creates a visual node:

```html
<head>
  <meta charset="UTF-8" />
  <meta name="title" content="My Page" />
  <meta name="description" content="An amazing description" />
  <meta name="slug" content="my-page" />
</head>
```

### 2. Manual Insertion

Use the slash command `/metadata` to manually insert a metadata block:

1. Type `/` to open the command menu
2. Type `metadata` or `meta`
3. Press Enter to insert

### 3. Editing

- Click the "Edit" button in the metadata block
- Modify the necessary fields
- Click "Save" to apply changes
- Use "Cancel" to discard changes

## Available Fields

### Page Title

- **Purpose**: Main page title for SEO
- **Type**: Free text
- **Recommendation**: 50-60 characters for optimal display
- **Tooltip**: Explains that this appears in browser tabs and search results

### URL Slug

- **Purpose**: Clean and friendly URL
- **Type**: Auto-formatted text (letters, numbers, and hyphens only)
- **Example**: "my-amazing-page"
- **Tooltip**: Explains how this becomes part of the web address

### Meta Description

- **Purpose**: Page description for search engines and social media
- **Type**: Textarea with 160 character limit
- **Recommendation**: 120-160 characters for optimal SEO
- **Tooltip**: Explains importance for search results and social previews
- **Real-time feedback**: Shows character count and SEO optimization status

## Interactive Tooltips & Tips

The extension includes comprehensive help through:

### 📝 Field-Level Tooltips

- **Title**: Explains browser tabs, search results, and character limits
- **Slug**: Explains URL structure and formatting rules
- **Description**: Explains SEO importance and optimal length

### 💡 SEO Tips Section

When editing, users see helpful SEO tips:

- Include main keywords in title and description
- Make titles unique and descriptive
- Write descriptions that encourage clicks
- Keep slugs short and readable

### ⚠️ Real-time Validation

- Character count for all fields
- SEO length warnings for title and description
- Auto-formatting feedback for slugs

## Usage Example

```typescript
// The editor automatically detects this HTML:
const htmlContent = `
<head>
  <meta charset="UTF-8">
  <meta name="title" content="JavaScript Guide">
  <meta name="description" content="Learn JavaScript from basics to advanced">
  <meta name="slug" content="javascript-guide">
</head>
<h1>My Article</h1>
<p>Article content...</p>
`;

// And automatically creates a visual metadata block at the top of the editor
```

## Integration

The extension is fully integrated into TiptapEditor and works automatically:

- ✅ Automatic extension imports
- ✅ useEditor configuration
- ✅ CSS styles included
- ✅ Slash command configured

## Extension Files

- `src/components/editor/extensions/MetadataNode.tsx` - Main node component
- `src/components/editor/extensions/MetadataExtension.tsx` - Helper extension
- `src/components/editor/TiptapEditor.css` - Specific styles

## Benefits

1. **Improved SEO**: Makes managing important metadata easier
2. **Consistent UX**: Interface integrated with the editor
3. **Error Prevention**: Prevents accidental deletion of metadata
4. **User Education**: Tooltips teach SEO best practices
5. **Productivity**: Quick editing without leaving context
6. **Responsiveness**: Works well on any device

## Available Commands

### Via Slash Menu

- `/metadata` - Insert metadata block (if it doesn't exist)
- `/meta` - Shortcut for metadata
- `/seo` - Another shortcut for metadata

### Keyboard Shortcuts

The metadata block can be selected with arrow keys but cannot be deleted with Delete/Backspace for protection.

## User Education Features

### 🎓 Built-in Learning

- Tooltips explain each field's purpose
- Real-time SEO feedback and warnings
- Character count guidance
- Best practices integrated into the UI

### 📊 SEO Optimization

- Automatic slug formatting
- Character count optimization
- Search result preview simulation
- Social media preview considerations

---

**Note**: This extension was developed specifically for the dashboard-cloudflare project and is fully integrated into the document editing system.
