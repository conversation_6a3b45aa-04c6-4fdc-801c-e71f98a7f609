import { format } from "date-fns";
import { BotQuestion } from "@/app/(home)/[projectId]/ask-ai/types";
import { Skeleton } from "@/components/ui/skeleton";
import { CalendarIcon } from "lucide-react";
import { RatingDisplay } from "./RatingDisplay";
import { AnswerStatusDisplay } from "./AnswerStatusDisplay";
import { SourcesDisplay } from "./SourcesDisplay";

interface TableComponentProps {
	questions: BotQuestion[];
	isLoading: boolean;
	onQuestionClick: (question: BotQuestion) => void;
}

export const TableComponent = ({
	questions,
	isLoading,
	onQuestionClick,
}: TableComponentProps) => {
	// Format date to a readable format
	const formatDate = (dateString: string) => {
		try {
			return format(new Date(dateString), "MM/dd/yyyy HH:mm");
		} catch {
			return dateString;
		}
	};

	if (isLoading) {
		return (
			<div className='w-full rounded-md border border-wd-border-color overflow-hidden'>
				<table className='w-full divide-y divide-wd-border-color'>
					<thead className='bg-muted'>
						<tr>
							<th className='px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-wd-font-color uppercase tracking-wider w-[45%]'>
								Question
							</th>
							<th className='px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-wd-font-color uppercase tracking-wider hidden md:table-cell w-[25%]'>
								Answer
							</th>
							<th className='px-3 sm:px-6 py-2 sm:py-3 text-center text-xs font-medium text-wd-font-color uppercase tracking-wider w-[10%]'>
								Sources
							</th>
							<th className='px-3 sm:px-6 py-2 sm:py-3 text-center text-xs font-medium text-wd-font-color uppercase tracking-wider w-[10%]'>
								Rating
							</th>
							<th className='px-3 sm:px-6 py-2 sm:py-3 text-center text-xs font-medium text-wd-font-color uppercase tracking-wider w-[10%]'>
								Could Answer
							</th>
						</tr>
					</thead>
					<tbody className='bg-white divide-y divide-wd-border-color'>
						{Array.from({ length: 5 }).map((_, index) => (
							<tr key={index}>
								<td className='px-3 sm:px-6 py-3 sm:py-4 whitespace-normal'>
									<Skeleton className='h-4 w-full max-w-[250px] mb-2' />
									<div className='flex items-center'>
										<CalendarIcon
											size={12}
											className='text-muted mr-1 flex-shrink-0'
										/>
										<Skeleton className='h-3 w-24 sm:w-32' />
									</div>
								</td>
								<td className='px-3 sm:px-6 py-3 sm:py-4 hidden md:table-cell'>
									<Skeleton className='h-4 w-full max-w-[180px]' />
								</td>
								<td className='px-3 sm:px-6 py-3 sm:py-4 text-center'>
									<Skeleton className='h-4 w-8 sm:w-10 mx-auto' />
								</td>
								<td className='px-3 sm:px-6 py-3 sm:py-4 text-center'>
									<Skeleton className='h-5 w-5 rounded-full mx-auto' />
								</td>
								<td className='px-3 sm:px-6 py-3 sm:py-4 text-center'>
									<Skeleton className='h-5 w-5 rounded-full mx-auto' />
								</td>
							</tr>
						))}
					</tbody>
				</table>
			</div>
		);
	}

	if (questions.length === 0) {
		return (
			<div className='flex flex-col items-center justify-center py-16 px-4 bg-white rounded-lg border border-gray-200 shadow-sm'>
				<div className='text-gray-400 mb-2'>
					<svg
						xmlns='http://www.w3.org/2000/svg'
						width='48'
						height='48'
						viewBox='0 0 24 24'
						fill='none'
						stroke='currentColor'
						strokeWidth='2'
						strokeLinecap='round'
						strokeLinejoin='round'
					>
						<circle cx='12' cy='12' r='10'></circle>
						<line x1='8' y1='12' x2='16' y2='12'></line>
					</svg>
				</div>
				<h3 className='text-lg font-medium text-gray-900 mb-1'>
					No results found
				</h3>
				<p className='text-gray-500 text-center max-w-md'>
					No questions match your selected filters. Try adjusting your filters
					to see more results.
				</p>
			</div>
		);
	}

	// Define the widths of the columns to be used consistently
	const colWidths = {
		question: "w-[45%]",
		answer: "w-[25%]",
		sources: "w-[10%]",
		rating: "w-[10%]",
		couldAnswer: "w-[10%]",
	};

	return (
		<div className='w-full relative rounded-md border border-wd-border-color'>
			<table className='w-full divide-y divide-wd-border-color'>
				<thead className='bg-muted sticky top-0 z-10 shadow-sm'>
					<tr>
						<th
							className={`px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-wd-font-color uppercase tracking-wider ${colWidths.question}`}
						>
							Question
						</th>
						<th
							className={`px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-wd-font-color uppercase tracking-wider hidden md:table-cell ${colWidths.answer}`}
						>
							Answer
						</th>
						<th
							className={`px-3 sm:px-6 py-2 sm:py-3 text-center text-xs font-medium text-wd-font-color uppercase tracking-wider ${colWidths.sources}`}
						>
							Sources
						</th>
						<th
							className={`px-3 sm:px-6 py-2 sm:py-3 text-center text-xs font-medium text-wd-font-color uppercase tracking-wider ${colWidths.rating}`}
						>
							Rating
						</th>
						<th
							className={`px-3 sm:px-6 py-2 sm:py-3 text-center text-xs font-medium text-wd-font-color uppercase tracking-wider ${colWidths.couldAnswer}`}
						>
							Could Answer
						</th>
					</tr>
				</thead>
				<tbody className='bg-white divide-y divide-wd-border-color'>
					{questions.map((q, index) => (
						<tr
							key={q.id || `question-${index}`}
							className='hover:bg-gray-50 cursor-pointer'
							onClick={() => onQuestionClick(q)}
						>
							<td
								className={`px-3 sm:px-6 py-3 sm:py-4 whitespace-normal ${colWidths.question}`}
							>
								<div className='text-xs sm:text-sm font-medium text-gray-900 line-clamp-2'>
									{q.standaloneQuestion || q.question}
								</div>
								<div className='text-[10px] sm:text-xs text-gray-500 flex items-center mt-1'>
									<CalendarIcon size={12} className='mr-1 flex-shrink-0' />
									<span className='truncate'>{formatDate(q.createdAt)}</span>
								</div>
							</td>
							<td
								className={`px-3 sm:px-6 py-3 sm:py-4 hidden md:table-cell whitespace-normal ${colWidths.answer}`}
							>
								<div className='text-xs sm:text-sm text-gray-900 line-clamp-3'>
									{q.answer.slice(0, 200)}
								</div>
							</td>
							<td
								className={`px-3 sm:px-6 py-3 sm:py-4 text-center ${colWidths.sources}`}
							>
								<div className='flex justify-center items-center'>
									<SourcesDisplay sources={q.sources || []} />
								</div>
							</td>
							<td
								className={`px-3 sm:px-6 py-3 sm:py-4 text-center ${colWidths.rating}`}
							>
								<div className='flex justify-center'>
									<RatingDisplay rating={q.rating} escalation={q.escalation} />
								</div>
							</td>
							<td
								className={`px-3 sm:px-6 py-3 sm:py-4 text-center ${colWidths.couldAnswer}`}
							>
								<div className='flex justify-center'>
									<AnswerStatusDisplay couldAnswer={q.couldAnswer} />
								</div>
							</td>
						</tr>
					))}
				</tbody>
			</table>
		</div>
	);
};
