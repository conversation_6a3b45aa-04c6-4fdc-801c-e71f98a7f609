// Define your Project type here. This is a basic example.
// You might want to keep importing it from "next/dist/build/swc/types"
// or define a more specific interface based on your Supabase table structure.

import { ConfigJson } from "@/types/sidebarConfig";

// Interface for the raw data from the 'projects' table
export interface ProjectRecord {
  id: number;
  project_name: string;
  owner_id: string;
  website_url: string;
  deploy_repo_url: string;
  source_repo_url: string;
  company_name: string | null;
  auto_config: boolean;
  docusaurus_configs: unknown | null;
  // Add other fields from 'projects' table if they exist and are used in payload.new/old
}

// Interface for the raw data from the 'project_user' table
export interface ProjectUserRecord {
  user_id: string;
  project_id: number; // This is the foreign key to projects.id
  role: string;
  is_creator: boolean;
  // Add other fields from 'project_user' table if they exist and are used
}

export interface Project {
  // Fields from the 'projects' table
  id: number;
  project_name: string;
  owner_id: string;
  website_url: string;
  deploy_repo_url: string;
  source_repo_url: string;
  company_name: string | null;
  auto_config: boolean;
  docusaurus_configs: unknown | null;
  plan_json: {
    docsbot: string;
  };
  configjson: ConfigJson | null;

  // Fields from the 'project_user' table
  user_id: string;
  project_id: number;
  role: string;
  is_creator: boolean;
}

export interface ProjectContextType {
  projects: Project[];
  setProjects: (projects: Project[]) => void;
  selectedProject: Project | null;
  setSelectedProject: (project: Project | null) => void;
  isLoading: boolean;
}
