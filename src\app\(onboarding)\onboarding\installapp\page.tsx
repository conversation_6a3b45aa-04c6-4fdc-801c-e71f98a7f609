"use client";

import { CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { IgithubOnboarding } from "@/app/interfaces/onboarding";
import { useEffect, useState } from "react";
import { createClient } from "@/utils/supabase/client";
import { useRouter } from "next/navigation";

export default function Installapp() {
	const router = useRouter();
	const supabase = createClient();

	const [errorMessage, setErrorMessage] = useState<string>();
	const [disableButton, setDisableButton] = useState(false);
	const [githubValues, setGithubValues] = useState<IgithubOnboarding>({
		companyName: "",
		orgSelected: "",
		orgId: null,
		projectName: "",
		repoLink: "",
		deployLink: "",
	});

	async function verifyInstallation() {
		setDisableButton(true);
		let lastResponseStatus: number;
		const intervalID = setInterval(async () => {
			const response = await fetch("/api/github/get-app-repo");
			lastResponseStatus = response.status;
			if (response.status === 200) {
				clearInterval(intervalID);
				clearTimeout(timeoutID);
				await redirectAfterInstall(response);
				setDisableButton(false);
			}
		}, 5000);
		const timeoutID = setTimeout(() => {
			clearInterval(intervalID);
			setDisableButton(false);
			if (lastResponseStatus === 401) {
				setErrorMessage(
					"Please give the application permission to access your repository"
				);
			}
		}, 120000);
	}

	const redirectAfterInstall = async (response: Response) => {
		try {
			const {
				projectName,
				ownerId,
				websiteUrl,
				deployRepoUrl,
				sourceRepoUrl,
				companyName,
			} = await response.json();

			const { error } = await supabase.from("projects").insert([
				{
					project_name: projectName,
					owner_id: ownerId,
					website_url: websiteUrl,
					deploy_repo_url: deployRepoUrl,
					source_repo_url: sourceRepoUrl,
					company_name: companyName,
				},
			]);

			if (error) {
				setErrorMessage(`Duplicated project name`);
				return;
			}

			return router.push("/onboarding/resume");
		} catch (e) {
			if (e instanceof Error) {
				setErrorMessage(JSON.stringify(e));
			}
		}
	};

	useEffect(() => {
		const fetchRepoData = async () => {
			const orgs = await fetch("/api/onboarding/get-github-cookie").then(
				async (res) => JSON.parse(await res.json())
			);
			setGithubValues(orgs);
			return orgs;
		};
		fetchRepoData();
	}, []);

	return (
		<CardContent>
			<h2 className='pt-3 text-lg font-semibold tracking-tight leading-none'>
				Install the WriteDocs app
			</h2>
			<div className='flex flex-col gap-4'>
				<p>
					WriteDocs created{" "}
					<a
						target='_blank'
						href={githubValues.repoLink}
						className='text-blue-500'
					>
						{githubValues.projectName}
					</a>{" "}
					at your {githubValues.orgSelected} organization.
				</p>
				<p>
					Install the{" "}
					<a
						target='_blank'
						href={"https://github.com/apps/WriteDocs-app"}
						className='text-blue-500'
					>
						WriteDocs app
					</a>{" "}
					into the {githubValues.projectName} repository.
				</p>
			</div>
			<Button
				disabled={disableButton}
				onClick={() => {
					if (githubValues.orgId) {
						window.open(
							`https://github.com/apps/writedocs-app/installations/new/permissions?target_id=${githubValues.orgId}`,
							"_blank"
						);
					} else {
						window.open(
							`https://github.com/apps/writedocs-app/installations/select_target`,
							"blank"
						);
					}
					verifyInstallation();
				}}
			>
				{!disableButton ? `Install App` : `Checking Installation`}
			</Button>
			{errorMessage && (
				<div className='text-sm font-medium text-destructive text-center'>
					{errorMessage}
				</div>
			)}
		</CardContent>
	);
}
