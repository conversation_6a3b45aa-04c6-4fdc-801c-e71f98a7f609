import handleInstallation from "@/utils/create-repo/installation";
import { createClient } from "@/utils/supabase/server";
import { Repository } from "@octokit/webhooks-types";
import { writedocsOctokit } from "@/utils/github/writedocsOctokit";
import { Buffer } from "buffer";

// fetch("/api/onboarding/project", {
//   method: "POST",
//   body: formData,
// })

export async function POST(request: Request): Promise<Response> {
	console.log(
		"[ONBOARDING API] POST /api/onboarding/project - Creating new project"
	);
	const formData = await request.formData();
	const formDataObj = Object.fromEntries(formData.entries());
	const projectName = `${formDataObj.projectName}-docs`;
	const websiteUrl = `${formDataObj.projectName}.docs.writedocs.io`;

	const supabase = createClient();

	const octokit = await writedocsOctokit();

	try {
		// verifica se o nome do projeto já existe
		const { data: projects, error } = await supabase
			.from("projects")
			.select("*")
			.eq("project_name", projectName);

		if (error) {
			throw new Error("Something went wrong");
		}

		if (projects.length > 0) {
			throw new Error("Project name already exist");
		}

		// tenta fazer o deploy do projeto, se erro o nome ja existe
		const { data: repoData } = await octokit.request(
			"POST /repos/{template_owner}/{template_repo}/generate",
			{
				template_owner: "writedocs",
				template_repo: "docusaurus-template-2",
				owner: "writedocs",
				name: projectName,
				description:
					"This is your template from writedocs generated by writedocs.io",
				include_all_branches: false,
				private: true,
				headers: {
					"X-GitHub-Api-Version": "2022-11-28",
				},
			}
		);

		// faz o deploy do projeto
		await handleInstallation(repoData as Repository);

		const {
			data: { user },
		} = await supabase.auth.getUser();

		// coloca o projeto na tabela projects e seleciona o ID retornado
		const { data: insertedProjectData, error: projectToDbError } =
			await supabase
				.from("projects")
				.insert({
					project_name: projectName,
					owner_id: user?.id,
					website_url: websiteUrl,
					deploy_repo_url: repoData.html_url,
					source_repo_url: repoData.html_url,
					company_name: null,
				})
				.select("id")
				.single();

		if (projectToDbError) {
			console.error(
				"[ONBOARDING API] Error inserting project:",
				projectToDbError
			);
			throw new Error("Failed to insert project into database");
		}

		if (!insertedProjectData || !insertedProjectData.id) {
			console.error("[ONBOARDING API] Failed to retrieve inserted project ID");
			throw new Error("Failed to retrieve project ID after insertion");
		}

		const newProjectId = insertedProjectData.id;
		console.log(`[ONBOARDING API] Project created with ID: ${newProjectId}`);

		// ----- Update files in the new GitHub repository -----
		const [owner, repo] = repoData.full_name.split("/");

		// Helper function to get file content and SHA from GitHub
		async function getGitHubFile(
			filePath: string
		): Promise<{ content: string; sha: string }> {
			const { data: fileData } = await octokit.request(
				"GET /repos/{owner}/{repo}/contents/{path}",
				{ owner, repo, path: filePath, ref: "main" }
			);

			if (
				!("content" in fileData) ||
				!("sha" in fileData) ||
				typeof fileData.content !== "string" ||
				typeof fileData.sha !== "string"
			) {
				throw new Error(`Could not retrieve content or SHA for ${filePath}`);
			}
			const content = Buffer.from(fileData.content, "base64").toString("utf8");
			return { content, sha: fileData.sha };
		}

		// Helper function to update file content in GitHub
		async function updateGitHubFile(
			filePath: string,
			newContent: string,
			sha: string,
			commitMessage: string
		): Promise<void> {
			await octokit.request("PUT /repos/{owner}/{repo}/contents/{path}", {
				owner,
				repo,
				path: filePath,
				message: commitMessage,
				content: Buffer.from(newContent).toString("base64"),
				sha: sha,
				branch: "main",
			});
		}

		// Update plan.json
		try {
			console.log(
				`[ONBOARDING API] Attempting to update plan.json for ${owner}/${repo}`
			);
			// Wait for repository template to be populated and plan.json to exist
			const { content: planJsonContent, sha: planJsonSha } =
				await (async () => {
					const retries = 5;
					for (let i = 1; i <= retries; i++) {
						try {
							return await getGitHubFile("plan.json");
						} catch (err: unknown) {
							if (
								typeof err === "object" &&
								err !== null &&
								"status" in err &&
								(err as { status: number }).status === 404
							) {
								console.log(
									`[ONBOARDING API] plan.json not found, retrying ${i}/${retries}`
								);
								await new Promise<void>((res) => setTimeout(res, 2000));
							} else {
								throw err;
							}
						}
					}
					throw new Error(`plan.json not found after ${retries} retries`);
				})();
			const planData = JSON.parse(planJsonContent);
			planData.projectID = newProjectId;
			const newPlanJsonContent = JSON.stringify(planData, null, 2);
			await updateGitHubFile(
				"plan.json",
				newPlanJsonContent,
				planJsonSha,
				"feat: add projectID to plan.json"
			);
			console.log(
				`[ONBOARDING API] Successfully updated plan.json for project ${newProjectId}`
			);
		} catch (error: unknown) {
			// If plan.json doesn't exist, create it with the projectID
			if (
				typeof error === "object" &&
				error !== null &&
				"status" in error &&
				(error as { status: number }).status === 404
			) {
				console.log(
					`[ONBOARDING API] plan.json not found, creating new plan.json for project ${newProjectId}`
				);
				const initialPlan = { projectID: newProjectId };
				const content = JSON.stringify(initialPlan, null, 2);
				await octokit.request("PUT /repos/{owner}/{repo}/contents/{path}", {
					owner,
					repo,
					path: "plan.json",
					message: "feat: create plan.json with projectID",
					content: Buffer.from(content).toString("base64"),
					branch: "main",
				});
				console.log(
					`[ONBOARDING API] Created plan.json for project ${newProjectId}`
				);
			} else {
				console.error(
					`[ONBOARDING API] Error updating plan.json for project ${newProjectId}:`,
					error
				);
			}
		}

		// Update src/utils/plan.js
		try {
			const planJsPath = "src/utils/plan.js";
			console.log(
				`[ONBOARDING API] Attempting to update ${planJsPath} for ${owner}/${repo}`
			);
			const { content: planJsContent, sha: planJsSha } = await getGitHubFile(
				planJsPath
			);

			// Update plan.js projectID: replace null, insert if missing, skip if already set
			if (planJsContent.includes('"projectID": null')) {
				// Replace placeholder null with actual newProjectId
				const newPlanJsContent = planJsContent.replace(
					/(["']projectID["']\s*:\s*)null/,
					`$1${newProjectId}`
				);
				await updateGitHubFile(
					planJsPath,
					newPlanJsContent,
					planJsSha,
					"fix: replace projectID null in plan.js"
				);
				console.log(
					`[ONBOARDING API] Replaced projectID null in ${planJsPath} for project ${newProjectId}`
				);
			} else if (!planJsContent.includes('"projectID"')) {
				// Insert projectID property for the first time
				const newPlanJsContent = planJsContent.replace(
					/(\s*)};?\s*$/,
					(match, leadingSpace) => {
						const contentBefore = planJsContent.substring(
							0,
							planJsContent.lastIndexOf(match)
						);
						const needsCommaBefore = !/{\s*$/.test(contentBefore.trimEnd());
						const comma = needsCommaBefore ? "," : "";
						const newLine = `${leadingSpace}  "projectID": ${newProjectId}`;
						return `${comma}\n${newLine}\n${leadingSpace}}`;
					}
				);
				await updateGitHubFile(
					planJsPath,
					newPlanJsContent,
					planJsSha,
					"feat: add projectID to plan.js"
				);
				console.log(
					`[ONBOARDING API] Added projectID to ${planJsPath} for project ${newProjectId}`
				);
			} else {
				console.log(
					`[ONBOARDING API] projectID already set in ${planJsPath}, skipping update for project ${newProjectId}`
				);
			}
		} catch (error) {
			console.error(
				`[ONBOARDING API] Error updating ${"src/utils/plan.js"} for project ${newProjectId}:`,
				error
			);
		}
		// ----- End of GitHub file updates -----
	} catch (error) {
		console.error("[ONBOARDING API] Error during project creation:", error);
		if (error instanceof Error) {
			return Response.json(
				{ error: "Project name already exist" },
				{ status: 403 }
			);
		}
		return Response.json({ error: "Something went wrong" }, { status: 500 });
	}

	return Response.json(formData);
}
