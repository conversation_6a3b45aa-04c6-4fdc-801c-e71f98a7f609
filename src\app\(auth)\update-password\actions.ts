"use server";

import { createClient } from "@/utils/supabase/server";
import { passwordSchema } from "@/lib/schemas";

export async function updateUserPassword(formData: FormData) {
  const supabase = createClient();

  const password = formData.get("password") as string;

  const validation = passwordSchema.safeParse(password);

  if (!validation.success) {
    return {
      success: false,
      error: {
        message:
          validation.error.issues[0]?.message || "Invalid password format.",
      },
    };
  }

  const { error } = await supabase.auth.updateUser({
    password: validation.data,
  });

  if (error) {
    return {
      success: false,
      error: {
        message:
          error.message || "Failed to update password. Please try again.",
      },
    };
  }

  return {
    success: true,
    message:
      "Password updated successfully. You can now log in with your new password.",
  };
}
